import React, { useState, useEffect, memo } from "react";
import { useParams } from "react-router-dom";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery, useMutation, useLazyQuery } from "@apollo/client";
import { GetStatus } from "gql/queries/GetStatus.gql";
import { AcceptRent } from "gql/mutations/AcceptRental.gql";
import { CarRecieved } from "gql/mutations/RecievedCar.gql";
import { ImageUpload } from "gql/mutations/UploadImage.gql";
import { FileUploader } from "components/ImageUploader";
import { AllyReceiveCar } from "gql/mutations/ChangeToInvoice.gql";
import { NotificationManager } from "react-notifications";
import { CloseRental } from "gql/mutations/CloseRental.gql";
import { Close, CheckCircle } from "@material-ui/icons";

import { RejectRentalDateExtensionRequest } from "gql/mutations/rejectExtension.gql";
import CustomTextField from "components/Input/CustomTextField";
import { CancelledReasons } from "gql/queries/CancelReasons.gql";
import { RejectedReasons } from "gql/queries/RejectReasons.gql";
import { RejectRental } from "gql/mutations/RejectRental.gql";

import List from "@material-ui/core/List";
import ListItem from "@material-ui/core/ListItem";
import ListItemIcon from "@material-ui/core/ListItemIcon";
import ListItemText from "@material-ui/core/ListItemText";
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from "reactstrap";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import ReasonsModal from "../ReasonsModel";
import store from "../../../../store";
import RejectModal from "../RejectModal";
import RequestRejectModal from "../AddEditBooking/rejectRequestModal";
function ChangeStatusModal({
  rentalDetails,
  rentalDateExtensionRequests,
  openModal,
  setOpenModal,
}) {
  const { bookingId } = useParams();
  const { ally_id } = store.getState()?.authUser.user;

  const [cancelledReasons, { loading, data: reasons }] = useLazyQuery(CancelledReasons);
  const [rejectedReasons, { data: rejectreasons }] = useLazyQuery(RejectedReasons);
  const [value, setValue] = React.useState(null);
  const [acceptRental] = useMutation(AcceptRent);
  const [carReceived] = useMutation(CarRecieved);
  const [imageUpload] = useMutation(ImageUpload);
  const [allyReceiveCar] = useMutation(AllyReceiveCar);
  const [rejectRental] = useMutation(RejectRental);
  const [image, setImage] = useState("");
  const [grandTotal, setGrangTotal] = useState(0);
  const [closeRental] = useMutation(CloseRental);
  const [rejectExtension] = useMutation(RejectRentalDateExtensionRequest);
  const { data, refetch } = useQuery(GetStatus, { variables: { id: bookingId } });
  const [Reasons, setReasons] = useState();
  const [openmodel, setOpenModel] = useState(false);
  const [rejectmodal, setRejectModal] = useState(false);
  const [isRequestRejectOpen, setIsRequestRejectOpen] = useState(false);
  const [note, setNote] = useState("");
  const [reason, setReason] = useState("");
  const [ImageWithInvoice, setImageWithInvoice] = useState();
  const { formatMessage } = useIntl();
  const [loader, setLoader] = useState(false);
  const [newValue, setNewValue] = useState();
  function successAction() {
    NotificationManager.success(<FormattedMessage id="StatusSucessfully" />);
    setLoader(false);
    refetch();
    setOpenModal(false);
  }
  useEffect(() => {
    if (reasons && value == "closed") {
      setReasons(reasons);
      setOpenModel(true);
    }
  }, [reasons]);
  useEffect(() => {
    if (rejectreasons && value == "rejected") {
      setRejectModal(true);
    }
  }, [rejectreasons]);
  useEffect(() => {
    if (rentalDetails?.rentalDetails?.invoicePic) {
      setImage(rentalDetails.rentalDetails.invoicePic);
    }
    if (rentalDetails?.rentalDetails?.newGrandTotal) {
      setGrangTotal(rentalDetails?.rentalDetails?.newGrandTotal);
    }
  }, [rentalDetails?.rentalDetails]);
  function ImageUpladThenCarRecived() {
    imageUpload({
      variables: { image, topic: "InvoiceImage" },
    })
      .then((res) => {
        if (
          res.data.imageUpload.status == "success" &&
          rentalDetails?.rentalDetails?.hasPendingExtensionRequests
        ) {
          swal({
            text: formatMessage({
              id: "Invoicing this booking will close the pending extensions as well",
            }),
            icon: "warning",
            buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
            dangerMode: true,
          }).then((response) => {
            if (response) {
              InvoiceRental(res.data.imageUpload.imageUpload.imageUrl);
            } else {
              setLoader(false);
              setValue(data?.rentalDetails.status);
            }
          });
        } else {
          swal({
            text: formatMessage({
              id: "Are you sure you want to invoice the booking? This action cannot be undone.",
            }),
            icon: "warning",
            buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
            dangerMode: true,
          }).then((response) => {
            if (response) {
              allyReceiveCar({
                variables: {
                  invoicePic: res.data.imageUpload.imageUpload.imageUrl,
                  newGrandTotal: grandTotal || 0,
                  rentalId: bookingId,
                },
              })
                .then((res) => {
                  if (res.data.allyReceiveCar.status == "success") {
                    successAction();
                  } else {
                    setValue(data?.rentalDetails.status);
                  }
                })
                .catch((err) => {
                  if (err?.message === "pending_extension_exists") {
                    // setisInvoicedModal(true);
                    swal({
                      text: formatMessage({
                        id: "Invoicing this booking will close the pending extensions as well",
                      }),
                      icon: "warning",
                      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
                      dangerMode: true,
                    }).then((res) => {
                      if (res) {
                        InvoiceRental();
                      } else {
                      }
                    });
                    setImageWithInvoice(res.data.imageUpload.imageUpload.imageUrl);
                    // setOpenModel(false);
                  } else {
                    NotificationManager.error(err?.message);
                    setValue("car_received");
                    setLoader(false);

                    refetch();
                  }
                });
            } else {
              setLoader(false);
              setValue("car_received");
            }
          });
        }
      })
      .catch((err) => {
        NotificationManager.error(err.message);
      });
  }

  function applyCarRevieved() {
    setLoader(true);
    return carReceived({
      variables: { rentalId: bookingId },
    }).then((res) => {
      if (res.data.carReceived.status == "success") {
        successAction();
      } else {
        NotificationManager.error(<FormattedMessage id="error" />);
      }
    });
  }

  function performAcceptRental() {
    setLoader(true);
    acceptRental({
      variables: { rentalId: bookingId },
    })
      .then((res) => {
        setLoader(false);
        if (res?.data?.acceptRental?.status == "success") {
          successAction();
        } else {
          NotificationManager.error(<FormattedMessage id=" Error " />);
        }
      })
      .catch((err) => NotificationManager.error(err.message));
    setOpenModal(false);
  }

  function rejectExtensionRequestHandler() {
    const rentalExtensionId = rentalDateExtensionRequests.find((i) => i.status === "pending").id;
    return rejectExtension({ variables: { rentalExtensionId } })
      .then((res) => {
        closeRental({
          variables: {
            rentalId: +bookingId,
            closeReasonId: +reason,
            note,
          },
        })
          .then((res) => {
            setIsRequestRejectOpen(false);
            successAction();
          })
          .catch((err) => {
            NotificationManager.error(err?.message);
          })
          .finally(() => {
            refetch();
          });
      })
      .catch((err) => {
        NotificationManager.error(err?.message);
        refetch();
      });
    setOpenModal(false);
  }
  function InvoiceRental(img = undefined) {
    allyReceiveCar({
      variables: {
        rentalId: bookingId,
        invoicePic: ImageWithInvoice || img || "",
        newGrandTotal: grandTotal || 0,
      },
    })
      .then((res) => {
        setLoader(false);

        successAction();
      })
      .catch((err) => NotificationManager.error(err?.message))
      .finally(() => {
        refetch();
        setOpenModal(false);
      });
  }

  function performCloseRent(reason, note) {
    if (!reason) {
      NotificationManager.error(<FormattedMessage id="please.selecte.reason" />);
      return;
    }
    if (+reason == 998 && !note.length) {
      NotificationManager.error(<FormattedMessage id="note.required" />);
      return;
    }

    closeRental({
      variables: {
        rentalId: +bookingId,
        closeReasonId: +reason,
        note,
      },
    })
      .then((res) => {
        setOpenModel(false);
        successAction();
      })
      .catch((err) => {
        if (err?.message === "pending_extension_exists") {
          setIsRequestRejectOpen(true);
          setOpenModel(false);
        } else {
          NotificationManager.error(err?.message);
          refetch();
        }
      });
    setOpenModal(false);
  }
  function rejectRent(reason, rejectedReason) {
    if (!reason) {
      NotificationManager.error(<FormattedMessage id="please.selecte.reason" />);
      return;
    }
    rejectRental({
      variables: {
        rejectedReason,
        rejectedReasons: reason,
        rentalId: bookingId,
      },
    })
      .then((res) => {
        setRejectModal(false);
        successAction();
      })
      .catch((err) => {
        NotificationManager.error(err?.message);
      });
  }

  function performAllyRecieveCar() {
    setLoader(true);
    if (rentalDetails?.rentalDetails?.hasPendingExtensionRequests) {
      swal({
        text: formatMessage({
          id: "Invoicing this booking will close the pending extensions are you sure you want to proceed? Please note that this action cannot be undone.",
        }),
        icon: "warning",
        buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
        dangerMode: true,
      }).then((res) => {
        if (res) {
          InvoiceRental();
        } else {
          setValue(data?.rentalDetails.status);

          setLoader(false);
        }
      });
    } else {
      swal({
        text: formatMessage({
          id: "Are you sure you want to invoice the booking? This action cannot be undone.",
        }),
        icon: "warning",
        buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
        dangerMode: true,
      }).then((res) => {
        if (res) {
          // InvoiceRental();
          allyReceiveCar({
            variables: {
              rentalId: bookingId,
              invoicePic: image,
              newGrandTotal: grandTotal || 0,
            },
          })
            .then((res) => {
              successAction();
              setLoader(false);
            })
            .catch((err) => {
              //  NotificationManager.error(err?.message)
              if (err?.message === "pending_extension_exists") {
                // setisInvoicedModal(true);

                setOpenModel(false);
              } else {
                NotificationManager.error(err?.message);
                refetch();
              }
            });
        } else {
          setLoader(false);
          setValue(data?.rentalDetails.status);
        }
      });
    }
    setOpenModal(false);
  }
  function applyCarRejected() {
    setLoader(true);
    rejectedReasons({
      variables: {
        status:
          data?.rentalDetails.status === "car_received"
            ? "car received"
            : data?.rentalDetails.status,
        userType: "ally",
      },
    }).then(() => {
      setLoader(false);
    });
    setRejectModal(true);
  }
  function OpenReasones() {
    const { ally_id } = store.getState()?.authUser.user;
    const rentalType = data?.rentalDetails?.isRentToOwn ? "rent_to_own" : "rental";
    if (ally_id) {
      cancelledReasons({
        variables: {
          rentalType,
          status:
            data?.rentalDetails.status == "car_received"
              ? "car received"
              : data?.rentalDetails.status,
          userType: "ally",
        },
      });
      setOpenModel(true);
    } else {
      cancelledReasons({
        variables: {
          rentalType,
          status:
            data?.rentalDetails.status == "car_received"
              ? "car received"
              : data?.rentalDetails.status,
          userType: data?.rentalDetails.status == "pending" ? "customer_care" : "customer_care",
        },
      });
      setOpenModel(true);
    }
  }

  useEffect(() => {
    setValue(data?.rentalDetails.status);
  }, [data]);

  const onChangeStatus = (value) => {
    setNewValue(value);
  };
  const ConfirmChangeStatus = () => {
    newValue === "confirmed"
      ? performAcceptRental()
      : newValue == "rejected"
      ? applyCarRejected()
      : newValue === "car_received"
      ? applyCarRevieved()
      : newValue === "invoiced"
      ? image.length > 0 && !image.includes("https")
        ? ImageUpladThenCarRecived()
        : performAllyRecieveCar()
      : null;
  };
  const InputHandelChange = (e) => {
    if (e.target.value.length) {
      const value = parseFloat(e.target.value);
      setGrangTotal(value);
    } else {
      setGrangTotal(null);
    }
  };

  function toggleHandler() {
    return setOpenModal(!openModal);
  }
  const Status = [
    {
      name: "PENDING",
      value: "pending",
      disabled: true,
    },
    {
      name: "CONFIRMED",
      value: "confirmed",
      disabled:
        loader ||
        (value !== "pending" && value != "closed" && value != "cancelled") ||
        data?.rentalDetails?.subStatus === "ally_declined",
    },

    {
      name: "CAR_RECEIVED",
      value: "car_received",
      disabled:
        value === "invoiced" ||
        loader ||
        value === "car_received" ||
        data?.rentalDetails?.subStatus === "ally_declined",
    },
    {
      name: "INVOICED",
      value: "invoiced",
      disabled:
        value == "cancelled" ||
        value == "closed" ||
        value === "invoiced" ||
        loader ||
        data?.rentalDetails?.subStatus === "ally_declined" ||
        ally_id,
    },
    value === "cancelled"
      ? {
          name: "CANCELLED",
          value: "cancelled",
          disabled: true,
        }
      : null,
    ally_id &&
    (rentalDetails?.rentalDetails?.status === "pending" ||
      rentalDetails?.rentalDetails?.status === "confirmed")
      ? {
          name: "REJECTED",
          value: "cancelled",
          disabled:
            (value === "pending" && data?.rentalDetails?.subStatus === "ally_declined") ||
            data?.rentalDetails?.subStatus === "ally_declined",
        }
      : null,
    !ally_id
      ? {
          name: "CLOSED",
          value: "closed",
          disabled: data?.rentalDetails.status == "closed" || value === "cancelled",
        }
      : null,
  ];
  return (
    <div>
      <Modal isOpen={openModal} toggle={toggleHandler}>
        <ModalHeader>
          <h2 style={{ fontSize: "24px" }}>
            <FormattedMessage id="change.status" />
          </h2>
          <Close style={{ cursor: "pointer" }} onClick={() => setOpenModal(false)} />
        </ModalHeader>
        <ModalBody>
          <>
            <RequestRejectModal
              isRequestRejectOpen={isRequestRejectOpen}
              setIsRequestRejectOpen={setIsRequestRejectOpen}
              rejectExtensionRequestHandler={rejectExtensionRequestHandler}
              setValue={setValue}
              orignalval={data?.rentalDetails.status}
            />
            <ReasonsModal
              openmodel={openmodel}
              setOpenModel={setOpenModel}
              reasons={reasons}
              setValue={setValue}
              orignalval={data?.rentalDetails.status}
              performCloseRent={performCloseRent}
              note={note}
              setNote={setNote}
              reason={reason}
              setReason={setReason}
              setOpenModal={setOpenModal}
            />

            <RejectModal
              openmodel={rejectmodal}
              setOpenModel={setRejectModal}
              reasons={rejectreasons}
              setValue={setValue}
              orignalval={data?.rentalDetails.status}
              rejectRent={rejectRent}
              setOpenModal={setOpenModal}
            />

            {/* {value && (
        <div>
          <div className="d-flex" style={{ flexWrap: "wrap", gap: "5px", marginBottom: "20px" }}>
            {
              <button
                className="btn btn-info mr-1 ml-1 btn-md"
                value="pending"
                disabled
                onClick={(e) => onChangeStatus(e.target.value)}
              >
                {value === "pending" && data?.rentalDetails?.subStatus !== "ally_declined" && (
                  <Check />
                )}
                <FormattedMessage id="PENDING" />
              </button>
            }
            {
              <button
                className="btn btn-success mr-1 ml-1 btn-md"
                value="confirmed"
                disabled={
                  loader ||
                  (value !== "pending" && value != "closed" && value != "cancelled") ||
                  data?.rentalDetails?.subStatus === "ally_declined"
                }
                onClick={(e) => onChangeStatus(e.target.value)}
              >
                {value === "confirmed" && <Check />}
                <FormattedMessage id="CONFIRMED" />
              </button>
            }

            <button
              className="btn btn-secondary mr-1 ml-1 btn-md"
              value="car_received"
              disabled={
                value === "invoiced" ||
                loader ||
                value === "car_received" ||
                data?.rentalDetails?.subStatus === "ally_declined"
              }
              onClick={(e) => onChangeStatus(e.target.value)}
            >
              {value === "car_received" && <Check />}
              <FormattedMessage id="CAR_RECEIVED" />
            </button>
            <button
              className="btn btn-primary mr-1 ml-1 btn-md"
              value="invoiced"
              disabled={
                value == "cancelled" ||
                value == "closed" ||
                value === "invoiced" ||
                loader ||
                data?.rentalDetails?.subStatus === "ally_declined"
                || ally_id
              }
              onClick={(e) => onChangeStatus(e.target.value)}
            >
              {value === "invoiced" && <Check />}
              <FormattedMessage id="INVOICED" />
            </button>
            {value === "cancelled" && (
              <button
                disabled
                className="btn btn-danger mr-1 ml-1 btn-md d-flex align-items-center"
                value="cancelled"
              >
                {value === "cancelled" && <Check />}
                <FormattedMessage id="CANCELLED" />
              </button>
            )}
            {ally_id &&
              (rentalDetails?.rentalDetails?.status === "pending" ||
                rentalDetails?.rentalDetails?.status === "confirmed") && (
                <button
                  className="btn btn-warning mr-1 ml-1 btn-md"
                  value="rejected"
                  onClick={(e) => onChangeStatus(e.target.value)}
                  disabled={
                    (value === "pending" && data?.rentalDetails?.subStatus === "ally_declined") ||
                    data?.rentalDetails?.subStatus === "ally_declined"
                  }
                >
                  {value === "pending" && data?.rentalDetails?.subStatus === "ally_declined" && (
                    <Check />
                  )}
                  <FormattedMessage id="REJECTED" />
                </button>
              )}
            {!ally_id && (
              <button
                className="btn btn-danger mr-1 ml-1 btn-md d-flex align-items-center"
                value="closed"
                onClick={() => OpenReasones()}
                disabled={data?.rentalDetails.status == "closed" || value === "cancelled"}
              >
                {value === "closed" && <Check />}
                <FormattedMessage id="CLOSED" />
              </button>
            )}
          </div>
          {(value === "car_received" || value === "invoiced") && (
            <div className="mt-4 col-sm-12 col-md-4">
              <div className="col-lg-auto" style={{ width: "200px" }}>
                <FileUploader titleId="InvoiceImage" image={image} setImage={setImage} />
              </div>
              <div className="col-lg-auto p-1">
                <CustomTextField
                  value={grandTotal}
                  name="grandTotal"
                  onChange={(e) => InputHandelChange(e)}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </> */}
            <List style={{ overflowY: "scroll", maxHeight: "300px" }}>
              <RadioGroup aria-label="gender" name="gender1" value={value}>
                {console.log(Status, "Status")}
                {Status &&
                  Status.filter((item) => item)?.map((item) => (
                    <ListItem
                      role={undefined}
                      dense
                      button
                      onClick={() => !item?.disabled && onChangeStatus(item.value)}
                      style={{ cursor: item?.disabled ? "not-allowed" : "pointer" }}
                    >
                      <ListItemIcon>
                        <FormControlLabel
                          value={item?.value}
                          control={
                            <Radio
                              style={{ minWidth: "13px !importnat " }}
                              checked={newValue ? item?.value == newValue : item?.value == value}
                              disabled={item?.disabled}
                              onChange={() => onChangeStatus(item.value)}
                            />
                          }
                          style={{ minWidth: "13px" }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={<FormattedMessage id={item?.name} />}
                        style={{ textAlign: "initial" }}
                      />
                    </ListItem>
                  ))}
              </RadioGroup>
            </List>
          </>
          {(value === "car_received" || value === "invoiced") && (
            <div className="mt-4 col-sm-12 col-md-4">
              <div className="col-lg-auto" style={{ width: "200px" }}>
                <FileUploader titleId="InvoiceImage" image={image} setImage={setImage} />
              </div>
              <div className="col-lg-auto p-1">
                <CustomTextField
                  value={grandTotal}
                  name="grandTotal"
                  onChange={(e) => InputHandelChange(e)}
                />
              </div>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            disabled={!newValue}
            onClick={() => {
              if (newValue == "closed") {
                OpenReasones();
              } else {
                ConfirmChangeStatus();
              }
            }}
          >
            <FormattedMessage id="Change" />
          </Button>
          <Button
            color="danger"
            onClick={() => {
              setOpenModal(false);
            }}
          >
            <FormattedMessage id="cancel" />
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default ChangeStatusModal;
