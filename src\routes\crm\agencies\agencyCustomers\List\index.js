/* eslint-disable prettier/prettier */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable no-undefined */
/** Agencies/Rentals Page */
import React from "react";
import CustomersList from "routes/crm/customers";

export default function AgencyCustomers() {
  return <CustomersList isAgency />;
}
