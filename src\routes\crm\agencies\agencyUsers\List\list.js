/* eslint-disable prettier/prettier */
/**
 * Bookings List
 */
import React, { useEffect, useState } from "react";
import { useIntl, FormattedMessage } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typo<PERSON>, Tooltip, Switch } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import { userCan } from "functions/userCan";
import { Data } from "./tableData";
import { NotificationManager } from "react-notifications";
import {ActivateAgencyUser} from "gql/mutations/ActivateAgencyUser.gql"
import { useMutation } from "@apollo/client";
import swal from "sweetalert";

function List({ AgencyUsersRes, loading, setPage, limit, setLimit, refetch, agency }) {
  const history = useHistory();

  const { formatMessage } = useIntl();
const [activateAgencyUser, { loading: ActivateLoader }] = useMutation(ActivateAgencyUser, {
    errorPolicy: "all",
  });
  const [agencyUsers, setAgencyUsers] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = agencyUsers;
  useEffect(() => {
    setAgencyUsers({
      collection: AgencyUsersRes?.agencyUsers?.collection,
      metadata: AgencyUsersRes?.agencyUsers?.metadata,
    });
  }, [AgencyUsersRes]);
  const actions = ({ id, isActive, user }) => (
    <div className="d-flex align-items-center">
      {agency?.isActive && userCan("agencies.update") && (
        <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
          <Link to={`users/${user?.id}/edit`}>
            <i className=" ti-pencil-alt m-1"></i>
          </Link>
        </Tooltip>
      )}
      
       <Switch
        checked={isActive}
        color="primary"
        name={user.id}
        onChange={(e)=>{
          if(e.target.checked){
            swal({
              title: formatMessage({ id: "are.u.sure.?" }),
              text: formatMessage({ id: "you want to activate this user" }),
              icon: "warning",
              buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
              dangerMode: true,
            }).then((result) => {

              if (result) {
                activateAgencyUser({
                  variables:{
                    input:{
                      agencyId:agency?.id,
                      id:user.id,
                      isActive:true
                    }
                  }
                }).then((res)=>{
                  if(res.errors?.length){
                    res.errors?.map((error)=>(
                      NotificationManager.error(<FormattedMessage id={error.message} />)    

                    ))
                   
                  }else{
                    refetch()
                    NotificationManager.success(<FormattedMessage id="Deactivated successfully" />);     
                  }
                  
                })
              }
            });
          }else{
            swal({
              title: formatMessage({ id: "are.u.sure.?" }),
              text: formatMessage({ id: "you want to deactivate this user" }),
              icon: "warning",
              buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
              dangerMode: true,
            }).then((willDelete) => {
              if (willDelete) {
                activateAgencyUser({
                  variables:{
                    input:{
                      agencyId:agency?.id,
                      id:user.id,
                      isActive:false
                    }
                  }
                }).then((res)=>{
                  if(res.errors?.length){
                    res.errors?.map((error)=>(
                      NotificationManager.error(<FormattedMessage id={error.message} />)    

                    ))
                   
                  }else{
                    refetch()
                    NotificationManager.success(<FormattedMessage id="Deactivated successfully" />);     
                  }
                       
                })
              }
            });
          }
        
        }}
        inputProps={{ "aria-label": "primary checkbox" }}
      />
    </div>
  );

  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={Data}
            loading={loading}
            tableRecords={collection}
            actions={actions}
            actionsArgs={["id", "isActive", "user"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around">
        {
          <>
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => setLimit(value)}
              perPage={limit}
              setPage={setPage}
            />
          </>
        }
      </div>
    </Typography>
  );
}

List.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  refetch: PropTypes.func,
  loading: PropTypes.bool,
  allyCompanies: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default List;
