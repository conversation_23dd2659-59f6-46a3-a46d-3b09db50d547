import React from "react";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

function TotalResults({ totalCount }) {
  return (
    <div className="total-results">
      <FormattedMessage
        id="components.total.results"
        values={{ count: totalCount }}
        defaultMessage="Total Results: {count}"
      />
    </div>
  );
}

TotalResults.propTypes = {
  totalCount: PropTypes.number.isRequired,
};

export default TotalResults;
