query GetAgenciesQuery(
  $email: String
  $id: Int
  $isActive: Boolean
  $limit: Int
  $name: String
  $page: Int
  $phoneNumber: String
) {
  agencies(
    email: $email
    id: $id
    isActive: $isActive
    limit: $limit
    name: $name
    page: $page
    phoneNumber: $phoneNumber
  ) {
    collection {
      agencyKey
      createdAt
      email
      id
      isActive
      logo
      name
      nameAr
      nameEn
      phoneNumber
      vatRegistrationId
    }
    metadata {
      currentPage
      limitValue
      totalCount
      totalPages
    }
  }
}
query GetAgenciesDropdownQuery {
  agencies {
    collection {
      id
      name
    }
  }
}
