/* eslint-disable prettier/prettier */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable no-undefined */
/** Agencies/Rentals Page */
import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useLocation, useHistory } from "react-router-dom";
import { Helmet } from "react-helmet";
import Button from "@material-ui/core/Button";
import { AgencyUsers as AgencyUsersQuery } from "gql/queries/AgencyUsers.gql";
import { AgencyDtails } from "gql/queries/AgencyDetails.gql";
import { useQuery } from "@apollo/client";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import { RctCard, RctCardContent } from "Components/RctCard";
import IntlMessages from "util/IntlMessages";
import { FiltersAndSearches } from "components/FiltersAndSearches";
import { userCan, getPageFromHash } from "functions";
import { Collapse } from "@material-ui/core";
import FilterListIcon from "@material-ui/icons/FilterList";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";
import List from "./list";
import { useSelector } from "react-redux";

export default function AgencyUsers() {
  const location = useLocation();
  const history = useHistory();
  const [count, setCount] = useState();
  const { formatMessage, messages } = useIntl();
  const [query, setQuery] = useState({});
  const [page, setPage] = useState(getPageFromHash(history) || 1);
  const [limit, setLimit] = useState(50);
  const { user } = useSelector((state) => state.authUser) || {};
  const { agency_id: agencyId } = useSelector((state) => state?.authUser?.user) || {};
  const { data: agency } = useQuery(AgencyDtails, {
    skip: !user?.agency_id,
    variables: { id: +user?.agency_id },
  });
  const {
    data: AgencyUsersRes,
    refetch,
    loading,
    error,
  } = useQuery(AgencyUsersQuery, {
    variables: {
      page,
      limit,
      ...query,
      isActive: query.isActive == "1" ? true : query.isActive == "0" ? false : undefined,
      agencyId: query?.id || agencyId,
      name: query?.["user.name"],
    },
    fetchPolicy: "no-cache",
  });

  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div className="clients-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "sidebar.users" })}</title>
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="sidebar.users" />}
        match={location}
        enableBreadCrumb
        extraButtons={
          <>
            {agency?.agency?.isActive && userCan("agencies.manage_users") && (
              <Button
                variant="contained"
                color="primary"
                className="btn btn-success"
                onClick={() => history.push("users/add")}
              >
                <IntlMessages id="create.new.something" values={{ something: messages?.user }} />
              </Button>
            )}
          </>
        }
      />

      <>
        <div className="d-flex">
          <div
            className="search-bar-wrap"
            style={{ width: "calc(100% - 70px)", visibility: !isCollapsed ? "hidden" : "visible" }}
          >
            <RctCard>
              <RctCardContent>
                <Collapse in={isCollapsed} timeout="auto">
                  <div className="mb-4">
                    <FiltersAndSearches
                      refetch={refetch}
                      setPage={setPage}
                      inBooking
                      forBooking
                      query={query}
                      setQuery={setQuery}
                      submitbtnid="search.filter"
                      count={count}
                      fields={[
                        { type: "search", name: "user.name" },
                        { type: "search", name: "email" },
                      ]}
                      filters={["agencyName"]}
                      is_active="isActive"
                      mobile
                      mobileRef="customerMobile"
                    />
                  </div>
                </Collapse>
                <div
                  style={{
                    flexGrow: 1,
                    width: "100%",
                  }}
                ></div>
              </RctCardContent>
            </RctCard>
          </div>
          <div className="d-flex justify-content-end">
            <Button
              className="d-flex justify-content-end mb-2 align-items-center"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <FilterListIcon />
              <span>
                <FormattedMessage id="Filter" />
              </span>
              {!isCollapsed ? <ExpandMoreIcon /> : <ExpandLessIcon />}
            </Button>
          </div>
        </div>
        <List
          refetch={refetch}
          loading={loading}
          setPage={setPage}
          page={page}
          setLimit={setLimit}
          limit={limit}
          AgencyUsersRes={AgencyUsersRes}
          agency={agency?.agency}
        />
      </>
    </div>
  );
}
