/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
/* eslint-disable react/jsx-no-bind */
import { InfoOutlined } from "@material-ui/icons";
import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Modal, ModalBody, ModalFooter } from "reactstrap";
import {CustomerUpdateRentalNote} from "gql/mutations/UpdateBookingNote.gql"
import {

    TextField,
  } from "@material-ui/core";
import { useMutation } from "@apollo/client";
import { NotificationManager } from "react-notifications";

function AddNoteModal({
  openNoteModal,
  setOpenNoteModal,
  rentalId,
  refetch,
  
}) {
  function toggleHandler() {
    return setOpenNoteModal(!openNoteModal);
  }
const [customerUpdateRentalNote]=useMutation(CustomerUpdateRentalNote)

const { locale, formatMessage } = useIntl();

const [note,setNote]=useState()
const AddNote=()=>{
    customerUpdateRentalNote({
      variables:{
        rentalId,
        note
  
      }
    }).then((res)=>{
      if(!res?.data.customerUpdateRentalNote.errors.length){
      NotificationManager.success(formatMessage({ id: "note added successfully" }));
      refetch()
      setOpenNoteModal(false)
      
      }
    })

}
  return (
    <div>
      <Modal isOpen={openNoteModal} toggle={toggleHandler}>
        <ModalBody>
          <div>
          <TextField
                            label={<FormattedMessage id="note" />}
                            multiline
                            className="w-100"
                            rows={4}
                            value={note}
                            variant="outlined"
                            onChange={(e) => {
                                setNote(e.target.value)
                              
                            }}
                          />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button disabled={!note?.length || !note?.trim().length} color="primary" onClick={()=>{
              AddNote()
            }} >
            <FormattedMessage id="Add"  />
          </Button>
          <Button
            color="danger"
            onClick={() => {
                setOpenNoteModal(false);
            }}
          >
            <FormattedMessage id="cancel" />
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default AddNoteModal;
