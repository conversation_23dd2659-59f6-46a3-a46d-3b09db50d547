
query FeedbackDetails($id:ID!) {
  feedbackDetails(id:$id) {
   
  id
  feedbackType
    feedbackAbout
    feedbackTopic{
          nameAr 
          nameEn
    }
    topicReason
    feedbackBank{
      name
    }
    accountFullName
    accountIban
    description 
    rental{
      bookingNo
      userId 
    }
    status 
    resolvingDate
    attachment
    feedbackReplies{
      repliedByName 
      replyAttachment 
      replyText
      createdAt
    }
    createdAt
}
  }
