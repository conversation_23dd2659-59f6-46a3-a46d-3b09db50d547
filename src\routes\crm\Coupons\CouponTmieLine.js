import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Header, <PERSON>dal<PERSON>ody, ModalFooter } from "reactstrap";
import { useQuery } from "@apollo/client";
import { CouponAudits } from "gql/queries/CouponTimeLine.gql"
import moment from "moment";
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import "./style.css";
// import { JSONParser } from "@amcharts/amcharts4/core";
const CouponTimeLine = (props) => {
  const { locale } = useIntl();

  const { className } = props;
  const [oldData, setOldData] = useState();
  const [newData, setNewData] = useState();
  const { data: couponAudit, refetch } = useQuery(CouponAudits, {
    skip: !props.CouponId,
    variables: { id:props?.CouponId},
  });
  useEffect(() => {
    if (couponAudit?.couponAudits?.length) {
      const newData = [];
      const oldData = [];
      couponAudit?.couponAudits?.map((rental, i) => {
        newData.push(rental?.newData);
        oldData.push(rental?.oldData);
      });
      setOldData(oldData);
      setNewData(newData);
    }
  }, [couponAudit]);
  useEffect(() => {
    if (!props.CouponId) {
      return;
    }
    refetch();
  }, [props.isOpen]);
  const toggle = () => props.setOpenTimeLineModal(!props.isOpen);

  return (
    <Modal isOpen={props.isOpen} toggle={toggle} className={className}>
      <ModalHeader toggle={toggle}>
        <FormattedMessage id="CouponTimeLine" />
      </ModalHeader>
      <ModalBody>
        {couponAudit?.couponAudits?.length ? (
          <div class="container" style={{ height: "500px", overflowY: "auto" }}>
            <div class="row">
              <div class="col-md-10">
                <ul class="cbp_tmtimeline" style={{direction: locale =="ar" ? "ltr" : ""}}>
                  <li style={{width:"90%"}}>
                    <div class="cbp_tmicon">
                      <i class="zmdi zmdi-account"></i>
                    </div>
                    <div class="cbp_tmlabel empty">
                      {" "}
                      <span style={{ fontWeight: "bold" }}>
                        <FormattedMessage id="coupon_id" /> :
                        {props.CouponId}
                      </span>{" "}
                    </div>
                  </li>

                  {couponAudit?.couponAudits.map((rental, index) => (
                    <li style={{width:"90%"}}>

                      <time
                        class="cbp_tmtime"
                        style={{ left: locale == "ar" ? "1px" : "" }}
                        datetime=""
                      >
                        <span>{rental.userName}</span>{" "}
                        <span style={{ direction: "ltr" }}>
                          {moment.utc(rental.createdAt).locale(locale).format("DD/MM/YYYY h:mm:ss a")}
                        </span>

                      </time>
                      <div class="cbp_tmicon bg-info">
                        <i class="zmdi zmdi-label"></i>
                      </div>
                       <div className="cbp_tmlabel">
                      
                      <div class=" d-flex" style={{ justifyContent: "space-between" }}>
                          
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="oldData" />
                          </h2>
                          <ul>
                          
                            {oldData &&
                              oldData[index] &&
                              Object.entries(oldData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? `${key}` : "0"} />}
                                    {": "}
                                    {
                                    val != null &&
                                    (key == "for_new_customers" || key == "is_active")
                                  
                                      ?                                       
                                     val ? 
                                     <CheckIcon style={{color:"green",verticalAlign:"middle"}}/>
                                     :  
                                  
                                     <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                    
                                    
                                      :
                                      key == "payment_brands" ? 
                                      <ul style={{paddingInline:"10px"}}>
                                      {
                                          val?.map((item)=>(
                                            <li>{item}</li>
                                          ))
                                        }
                                        </ul>
                                      : 
                                      val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{"  "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="newData" />
                          </h2>
                          <ul>
                            {newData &&
                              newData[index] &&
                              Object.entries(newData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? key : "0"} />} :{" "}
                                    {
                                    val != null &&
                                    (key == "for_new_customers" || key == "is_active")
                                      
                                      ?
                                      val  ?
                                        <CheckIcon style={{color:"green",verticalAlign:"middle"}} />
                                      :  
                                   
                                      <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                      
                                    
                                      :
                                      key == "payment_brands" ? 
                                      <ul style={{paddingInline:"10px"}}>
                                        {
                                          val?.map((item)=>(
                                            <li>{item}</li>
                                          ))
                                        }
                                        </ul>
                                        :
                                       val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{" "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                      </div>
                      </div>

                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ) : (
          <div className="d-flex" style={{ justifyContent: "center" }}>
            <FormattedMessage id="No data found" />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>
          <FormattedMessage id="close"/>
        </Button>
      </ModalFooter>
    </Modal>
  );
};
export default CouponTimeLine;
