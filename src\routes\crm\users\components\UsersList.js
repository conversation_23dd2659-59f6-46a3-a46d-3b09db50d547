/* eslint-disable react/prop-types */
/* eslint-disable prettier/prettier */
/** Users List */
import React, { useEffect, useState } from "react";
import MUIDataTable from "mui-datatables";
import { FormattedMessage, useIntl } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { CircularProgress, Tooltip } from "@material-ui/core";
import useSetState from "hooks/useSetState";
import { MUIDataTableOptions } from "constants/constants";
import { Pagination } from "@material-ui/lab";
import PerPage from "components/shared/PerPage";
import TotalResults from "components/shared/TotalResults";
import { userCan } from "functions/userCan";
import swal from "sweetalert";
import { NotificationManager } from "react-notifications";
import { DeleteUser } from "gql/mutations/DeleteUser.gql";
import { useMutation } from "@apollo/client";
import "./styles.scss";
import { Delete, Details, Edit, History, Info } from "@material-ui/icons";
import UsersTimeLine from "../UsersTimeLine";

const sortableColumns = ["name"];
const tableColumns = ["name", "email.address", "status", "roles", "common.actions"];
function UsersList({ usersRes, loading, setPage, limit, setLimit, refetch }) {
  const history = useHistory();
  const { formatMessage: f, locale } = useIntl();
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);

  const [userId, setUserId] = useState();
  const [bookingsState, setBookingsState] = useSetState({
    collection: [],
    metadata: {},
  });

  const [deleteUser] = useMutation(DeleteUser);

  // Columns Headers
  const columns = tableColumns.map((x) => ({
    name: <div style={{ textTransform: "capitalize" }}>{f({ id: x })}</div>,
    options: {
      sort: sortableColumns.includes(x),
    },
  }));
  const handelDeleteUser = (id) => {
    swal({
      title: f({ id: "are.u.sure.?" }),
      text: f({ id: "u.want.delete.user" }),
      icon: "warning",
      buttons: [f({ id: "cancel" }), f({ id: "delete" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        deleteUser({
          variables: {
            input: { id: +id },
          },
        })
          .then(() => refetch())
          .then((res) =>
            swal(f({ id: "userdeletedsuccessfully" }), {
              icon: "success",
            }),
          )
          .catch((error) => {
            NotificationManager.error(`${error?.message}`);
          });
      }
    });
  };
  const getRentalAudits = (id) => {
    setUserId(id);
    setOpenTimeLineModal(true);
  };
  // Actions
  const actions = ({ id }) => (
    <>
      {userCan("users.show") && (
        <Tooltip title={f({ id: "common.details" })} placement="top">
          <Link to={`users/${id}`}>
            <Info style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }} />
          </Link>
        </Tooltip>
      )}
      {userCan("users.update") && (
        <Tooltip title={f({ id: "common.edit" })} placement="top">
          <Link to={`users/${id}/edit`}>
            <Edit style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }} />
          </Link>
        </Tooltip>
      )}
      {userCan("users.delete") && (
        <Tooltip title={f({ id: "common.delete" })} placement="top">
          <Delete
            onClick={() => handelDeleteUser(id)}
            style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }}
          />
        </Tooltip>
      )}
      <Tooltip title={f({ id: "common.timeline" })} placement="top">
        <Link>
          <History
            style={{ fontSize: "18px", color: "#5d89d8" }}
            onClick={() => getRentalAudits(id)}
          />
        </Link>
      </Tooltip>
    </>
  );

  // Table options
  const options = {
    ...MUIDataTableOptions,
    textLabels: {
      body: {
        // eslint-disable-next-line no-nested-ternary
        noMatch: loading ? (
          <CircularProgress />
        ) : locale === "en" ? (
          "No data to show"
        ) : (
          "لا يوجد بيانات للعرض"
        ),
      },
    },
    count: bookingsState?.metadata?.totalCount || -1,

    onColumnSortChange: (col, dir) => {},
    onTableChange: (action, tableState) => {
      if (tableState.rowsPerPage !== limit) {
        setLimit(tableState.rowsPerPage);
      }
      if (action === "changePage") {
        setPage(tableState.page);
      }
    },
    customFooter: (count, page, rowsPerPage, changeRowsPerPage, changePage) => (
      <div className="d-flex justify-content-around align-items-center m-2">
        {bookingsState?.metadata?.currentPage && (
          <>
            <TotalResults totalCount={bookingsState?.metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(count / rowsPerPage)}
              page={bookingsState?.metadata?.currentPage}
              onChange={(_, value) => {
                setPage(value);
                changePage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => {
                setLimit(value);
                changeRowsPerPage(value);
              }}
              setPage={setPage}
              perPage={rowsPerPage}
            />
          </>
        )}
      </div>
    ),
  };

  useEffect(() => {
    setBookingsState({
      collection: usersRes?.users?.collection.map((user) => [
        `${user.firstName} ${user.lastName}`,
        user.email,
        <span className={`badge badge-${user?.isActive ? "success" : "danger"}`}>
          <FormattedMessage id={user?.isActive ? "active" : "inactive"} />
        </span>,
        user.roles.map((role) => (
          <span className="badge badge-secondary m-1">{role[`${locale}Name`]}</span>
        )),
        actions({ id: user.id }),
      ]),
      metadata: usersRes?.users?.metadata,
    });
  }, [usersRes]);

  return (
    <div className="mb-4 users-table">
      <MUIDataTable data={bookingsState.collection} columns={columns} options={options} />
      <UsersTimeLine
        CustomerId={userId}
        isOpen={opneTimeLineModal}
        setOpenTimeLineModal={setOpenTimeLineModal}
      />
    </div>
  );
}

UsersList.propTypes = {
  setPage: PropTypes.func,
  loading: PropTypes.bool,
  usersRes: PropTypes.object,
  setLimit: PropTypes.func,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default UsersList;
