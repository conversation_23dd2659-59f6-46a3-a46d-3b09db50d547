/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { Insurances } from "gql/queries/GetInsurances.gql";
import { persist } from "constants/constants";

function InsurancesDropDown({
  loading,
  setSelectedInsurance,
  selectedInsurance,
  error,
  valueAttribute,
  allyId,
  multiple,
  required,
  isAlly,
  manageradd,
  areaIds,
  noSkip,
  isDisabled,
  coupon,
  InsurancesDropDown,
  setList,
  banner,
  cities,
  ...props
}) {
  const { data: allinsurances, loading: gettingModels } = useQuery(Insurances);
  useEffect(() => {
    if (!selectedInsurance) {
      onClear();
    }
  }, [selectedInsurance]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  const { formatMessage } = useIntl();
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (allinsurances && setList) {
      setList(allinsurances?.insurances?.collection?.length);
    }
  }, [allinsurances]);

  useEffect(() => {
    const alloptions =
      allinsurances?.insurances.map((x) => ({
        value: x[valueAttribute || "enName"],
        label: x.name,
      })) || [];
    if (alloptions.length && multiple) {
      setOptions([
        { value: "0", label: formatMessage({ id: "noInsurance" }) },
        ...alloptions
      ]);
    } else if(alloptions.length) {
      setOptions([
        { value:"0", label: formatMessage({ id: "noInsurance" })  },
        ...alloptions
      ]);
    } 
  }, [allinsurances]);

  return (
    <Select
      ref={selectInputRef}
      isMulti={multiple}
      isDisabled={isDisabled}
      isClearable
      className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
        error ? "selection-error" : ""
      }`}
      options={options}
      loadOptions={gettingModels || loading}
      value={
        multiple
          ? options?.filter((optn) => selectedInsurance?.includes(+optn.value))
          : options?.find((optn) => `${optn.value}` === `${selectedInsurance}`)
      }
      placeholder={ formatMessage({ id: "insurancesType" })}
      onChange={(selection) => {
        if (multiple) {
          const insuranceIds = [];
          if (selection == null && multiple) {
            setSelectedInsurance();
            return;
          }
          if (selection[0]?.value === "all" || selection[selection.length - 1]?.value === "all") {
            options.map(
              (onselectoion) => onselectoion.value !== "all" && insuranceIds.push(+onselectoion?.value),
            );
          }
          selection?.map((onselectoion) => insuranceIds.push(+onselectoion?.value));
          if (insuranceIds.length) {
            const versions = insuranceIds.filter((id) => !isNaN(id));
            setSelectedInsurance([...versions]);
          } else {
            setSelectedInsurance([]);
          }
        } else {
          if (selection?.value === "all") {
            setSelectedInsurance("null");
            return;
          }
          setSelectedInsurance(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingModels) {
          return <CircularProgress />;
        }
        if (!allinsurances?.length) return <FormattedMessage id="No data found" />;
      }}
      {...props}
    />
  );
}

InsurancesDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedInsurance: PropTypes.string,
  setSelectedInsurance: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};

export default memo(InsurancesDropDown);