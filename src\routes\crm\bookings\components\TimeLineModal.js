/* eslint-disable array-callback-return */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dalHeader, <PERSON>dal<PERSON>ody, ModalFooter } from "reactstrap";
import { useQuery } from "@apollo/client";
import { RentalsAudits } from "gql/queries/BookingTimeLine.gql";
import {
  GetRentalPaymentTransactions,
  GetRentalDetailsQuery,
} from "gql/queries/Rental.queries.gql";
import moment from "moment";
import { Tab, Tabs, TabList, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import "./style.css";
// import { JSONParser } from "@amcharts/amcharts4/core";
const TimeLine = (props) => {
  const { locale } = useIntl();

  const { className } = props;
  const [oldData, setOldData] = useState();
  const [newData, setNewData] = useState();

  const { data: rentalAudits, refetch } = useQuery(RentalsAudits, {
    skip: !props.BookingId,
    variables: { id: props.BookingId },
  });
  const { data: rentalDetailsReasponse } = useQuery(GetRentalDetailsQuery, {
    skip: !props.BookingId,
    variables: { id: props?.BookingId },
  });
  const { data: rentalDetails } = useQuery(GetRentalPaymentTransactions, {
    skip: !props.BookingId,
    variables: { id: props.BookingId },
  });
  const isJSONString = (str) => {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  };
  useEffect(() => {
    if (rentalAudits?.rentalsAudits?.length) {
      const newData = [];
      const oldData = [];
      rentalAudits?.rentalsAudits?.map((rental, i) => {
        if (rental?.newData) {
          newData.push(JSON.parse(rental?.newData));
        }
        if (rental?.oldData) {
          oldData.push(JSON.parse(rental?.oldData));
        }
      });
      setOldData(oldData);
      setNewData(newData);
    }
  }, [rentalAudits]);
  useEffect(() => {
    if (!props.BookingId) {
      return;
    }
    refetch();
  }, [props.isOpen]);
  const toggle = () => props.setOpenTimeLineModal(!props.isOpen);
  return (
    <Modal isOpen={props.isOpen} toggle={toggle} className={className}>
      <ModalHeader toggle={toggle}>
        <FormattedMessage id="BookingTimeLine" />
      </ModalHeader>
      <ModalBody>
        <Tabs>
          <div style={{ display: "flex", justifyContent: "center", marginBottom: "30px" }}>
            <TabList>
              <Tab>
                <FormattedMessage id="basicinformation" />
              </Tab>
              <Tab>
                <FormattedMessage id="Financial Transaction" />
                {/* paymentTransactions: */}
              </Tab>
            </TabList>
          </div>
          <TabPanel>
            {rentalAudits?.rentalsAudits?.length ? (
              <div className="container" style={{ height: "500px", overflowY: "auto" }}>
                <div className="row">
                  <div className="col-md-10">
                    <ul
                      className="cbp_tmtimeline"
                      style={{ direction: locale == "ar" ? "ltr" : "" }}
                    >
                      <li style={{ width: "90%" }}>
                        <div className="cbp_tmicon">
                          <i className="zmdi zmdi-account"></i>
                        </div>
                        <div className="cbp_tmlabel empty">
                          {" "}
                          <span style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="bookingId.placeholder" /> :
                            {rentalAudits?.rentalsAudits[0].rentalId}
                          </span>{" "}
                        </div>
                      </li>

                      {rentalAudits?.rentalsAudits.map((rental, index) => (
                        <li style={{ width: "90%" }}>
                          <time
                            className="cbp_tmtime"
                            style={{ left: locale == "ar" ? "1px" : "" }}
                            dateTime=""
                          >
                            <span>{rental.userName}</span>{" "}
                            <span style={{ direction: "ltr" }}>
                              {moment.utc(rental.createdAt).local().format("DD/MM/YYYY h:mm:ss a")}
                            </span>
                            <span>
                              {/* {!rental?.userRole?.includes("invalid") &&!rental?.userRole?.includes("المستخدم")&& 
                            rental?.userRole && isJSONString(rental?.userRole) && 
                            // JSON.parse(rental?.userRole).map((role) => role)
                            Array.isArray( JSON.parse(rental?.userRole)) ? 

                            JSON.parse(rental?.userRole)?.map((role) => role)
                            :  ""
                          
                            } */}
                            </span>
                          </time>
                          <div className="cbp_tmicon bg-info">
                            <i className="zmdi zmdi-label"></i>
                          </div>
                          <div className="cbp_tmlabel">
                            {rental?.referenceNo ? (
                              <div style={{ direction: "rtl" }}>
                                <span>
                                  <FormattedMessage id="Extension.id" /> :{rental?.referenceNo}
                                </span>
                              </div>
                            ) : null}

                            <div className=" d-flex" style={{ justifyContent: "space-between" }}>
                              <div
                                className="w-50"
                                style={{ direction: locale == "ar" ? "rtl" : "ltr" }}
                              >
                                <h2 style={{ fontWeight: "bold" }}>
                                  <FormattedMessage id="oldData" />
                                </h2>
                                <ul>
                                  {oldData &&
                                    oldData[index] &&
                                    Object.entries(oldData[index]).map(([key, val]) => (
                                      <>
                                        <li style={{ width: "90%" }}>
                                          <FormattedMessage id={key ? `${key}` : "0"} />
                                          {": "}
                                          {val &&
                                          val != null &&
                                          (key === "pick_up_time" ||
                                            key === "drop_off_time" ||
                                            key == "refunded_at") ? (
                                            moment(oldData[index][key], "HHmmss").format(
                                              "HH:mm:ss a",
                                            )
                                          ) : key == "decline_reason" ? (
                                            <>
                                              <ul style={{ paddingInline: "10px" }}>
                                                {val?.split(",").map((value) => (
                                                  <li> {value}</li>
                                                ))}
                                              </ul>
                                            </>
                                          ) : key == "notes" && Array.isArray(val) ? (
                                            val?.map((item) => item.note)
                                          ) : (
                                            val != null &&
                                            (val?.length || val) && <FormattedMessage id={val} />
                                          )}
                                          {"  "}
                                        </li>
                                      </>
                                    ))}
                                </ul>
                              </div>
                              <div
                                className="w-50"
                                style={{ direction: locale == "ar" ? "rtl" : "ltr" }}
                              >
                                <h2 style={{ fontWeight: "bold" }}>
                                  <FormattedMessage id="newData" />
                                </h2>
                                <ul>
                                  {newData &&
                                    newData[index] &&
                                    Object.entries(newData[index]).map(([key, val]) => (
                                      <>
                                        <li style={{ width: "90%" }}>
                                          <FormattedMessage id={key || "0"} /> :{" "}
                                          {val &&
                                          val != null &&
                                          (key === "pick_up_time" ||
                                            key === "drop_off_time" ||
                                            key === "refunded_at") ? (
                                            moment(newData[index][key], "HHmmss").format(
                                              "HH:mm:ss a",
                                            )
                                          ) : key == "decline_reason" ? (
                                            <>
                                              <ul style={{ paddingInline: "10px" }}>
                                                {val?.split(",").map((value) => (
                                                  <li> {value}</li>
                                                ))}
                                              </ul>
                                            </>
                                          ) : key == "notes" && Array.isArray(val) ? (
                                            val != null && val?.map((item) => item.note)
                                          ) : (
                                            val != null &&
                                            (val?.length || val) && <FormattedMessage id={val} />
                                          )}{" "}
                                        </li>
                                      </>
                                    ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ) : (
              <div className="d-flex" style={{ justifyContent: "center" }}>
                <FormattedMessage id="No data found" />
              </div>
            )}
          </TabPanel>
          <TabPanel>
            <div>
              <FormattedMessage id="Financial Transaction" />
            </div>
            <div className="paymentTransaction">
              <table className="table table-bordered">
                <thead>
                  <tr>
                    <th>
                      <FormattedMessage id="FT ID" />
                    </th>
                    <th>
                      <FormattedMessage id="Details" />
                    </th>
                    <th>
                      <FormattedMessage id="Amount" />
                    </th>
                    <th>
                      <FormattedMessage id="transactionType" />
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {rentalDetails?.rentalDetails?.paymentTransactions?.map((item) => (
                    <tr key={item.transactionNo}>
                      <td>{item.transactionNo}</td>
                      <td>{item.transactionDetails}</td>
                      <td>{item.amount}</td>
                      <td>{item.type}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div
              className="paymentTransaction"
              style={{ display: "flex", justifyContent: "center" }}
            >
              <table
                className="table"
                style={{ width: "auto", margin: "20px 0", border: "1px solid #ccc" }}
              >
                <tbody>
                  <tr>
                    <td>
                      <FormattedMessage id="Total paid amount" />:
                    </td>
                    <td>{rentalDetailsReasponse?.rentalDetails?.paidAmount}</td>
                  </tr>
                  <tr>
                    <td>
                      <FormattedMessage id="Total remaining amount" />:
                    </td>
                    <td>{rentalDetailsReasponse?.rentalDetails?.paymentsTotalRemainingAmounts}</td>
                  </tr>
                  <tr>
                    <td>
                      <FormattedMessage id="Total charged amount" />:
                    </td>
                    <td>{rentalDetailsReasponse?.rentalDetails?.paymentsTotalChargedAmounts}</td>
                  </tr>
                  <tr>
                    <td>
                      <FormattedMessage id="Booking Total" />:
                    </td>
                    <td>{rentalDetailsReasponse?.rentalDetails?.totalBookingPrice}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </TabPanel>
        </Tabs>
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>
          <FormattedMessage id="close" />
        </Button>
      </ModalFooter>
    </Modal>
  );
};
export default TimeLine;
