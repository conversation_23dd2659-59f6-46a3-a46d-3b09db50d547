query Agency($id: ID!) {
  agency(id: $id) {
    allyCompanyIds
    email
    isActive
    logo
    nameAr
    nameEn
    phoneNumber
    vatRegistrationId
    agencyKey
  }
}

query AgencyCustomersQuery(
  $agencyId: ID!
  $email: String
  $isActive: Boolean
  $limit: Int
  $mobile: String
  $nid: String
  $name: String
  $page: Int
  $status: String
) {
  agencyCustomers(
    agencyId: $agencyId
    email: $email
    isActive: $isActive
    limit: $limit
    mobile: $mobile
    nid: $nid
    name: $name
    page: $page
    status: $status
  ) {
    collection {
      createdAt
      agencyId
      isActive
      user {
        createdAt
        id
        mobile
        isActive
        name
        email
        customerProfile {
          nid
          passportNumber
        }
      }
    }
    metadata {
      currentPage
      totalCount
      totalPages
    }
  }
}

query AgencyUser($agencyId: ID, $id: ID) {
  agencyUser(agencyId: $agencyId, id: $id) {
    email
    firstName
    isActive
    lastName
    user {
      id
      isActive
      mobile
    }
    userType
  }
}

query AgencyTransactions($page: Int, $limit: Int) {
  agencyTransactions {
    balance
    walletTransactions(page: $page, limit: $limit) {
      metadata {
        totalCount
        currentPage
        limitValue
        totalPages
      }
      collection {
        id
        amount
        bookingNo
        transactionNo
        transactionDate
        transactionType
        isIncoming
        walletSource {
          id
          name
          img
        }
        currentBalance
      }
    }
  }
}
query UserWallet($page: Int, $limit: Int, $type: String, $userId: Int!) {
  userWallet(type: $type, userId: $userId) {
    balance
    walletTransactions(page: $page, limit: $limit) {
      metadata {
        totalCount
        currentPage
        limitValue
        totalPages
      }
      collection {
        id
        amount
        bookingNo
        transactionNo
        transactionDate
        transactionType
        isIncoming
        walletSource {
          id
          name
          img
        }
        currentBalance
      }
    }
  }
}

query ActiveAgencies{
  activeAgencies {
    agencyKey
    allyCompanyIds
    createdAt
    email
    id
    isActive
    logo
    name
    nameAr
    nameEn
    phoneNumber
    vatRegistrationId
  }
}
