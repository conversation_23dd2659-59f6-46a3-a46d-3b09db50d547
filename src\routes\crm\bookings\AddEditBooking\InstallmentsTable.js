/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable no-nested-ternary */
/* eslint-disable prefer-const */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import Select from "react-select";

import {
  MuiPickersUtilsProvider,
  DateTimePicker as MaterialDateTimePicker,
} from "@material-ui/pickers";
import moment from "moment";
import { UpdateInstallmentStatus } from "gql/mutations/UpdateInstallmentStatus.gql";
import MomentUtils from "@date-io/moment";
import { TextField } from "@material-ui/core";
import { useQuery, useMutation } from "@apollo/client";
import { Edit, Refresh } from "@material-ui/icons";
import { RecallPaymentGateway } from "gql/queries/RecallPaymentGateway.gql";
import { NotificationManager } from "react-notifications";
import { userCan } from "functions";
import SendIcon from "@material-ui/icons/Send";
import RiyalComponent from "components/shared/RiyalComponent";
import useResendToAlly from "../hooks/useResendToAlly";
import store from "../../../../store";
import RefundInstallmentComponent from "./RefundInstallment";
import PaymentLink from "../components/PaymentLink";
import Button from "@material-ui/core/Button";
import AddNoteModal from "../booking-details/AddNoteModal";

export default function InstallmentsTable({ Installments, refetchBooking, rentalId, bookDetails }) {
  const { formatMessage, locale } = useIntl();
  const [updateInstallmentStatus] = useMutation(UpdateInstallmentStatus);
  const { sendInstallmentToAlly } = useResendToAlly();
  const [InstallmentId,setInstallmentId]=useState()
  const [openNoteModal, setopenNoteModal] = useState();

  const InstallmentData = Installments?.map((item) => ({
    amount: item.amount,
    canSendToAlly: item.canSendToAlly,
    dueDate: item.dueDate,
    id: item.id,
    installmentNumber: item.installmentNumber,
    lastPaidInstallment: item.lastPaidInstallment,
    paymentMethod: item.paymentMethod,
    paymentStatus: item.paymentStatus,
    status: item.status,
    statusLocalized: item.statusLocalized,
    totalAmountPaidAt: item?.totalAmountPaidAt,
    walletPaidAmount: item.walletPaidAmount,
    loyaltyCollections: item.loyaltyCollections,
    hasPendingPaymentTransaction: item.hasPendingPaymentTransaction,
    payable: item.payable,
    paymentLink: item.paymentLink,
    paymentLinkToken: item.paymentLink?.token,
    paymentLinkValidForPayment: item.paymentLink?.validForPayment,
  }));

  const { ally_id } = store.getState()?.authUser.user;
  const { refetch: _recallPaymentGateway } = useQuery(RecallPaymentGateway, { skip: true });

  const updateRequestHandler = (record, index) => {
    if (
      record.statusChanged &&
      record?.statusChanged?.toLowerCase() != InstallmentData[index]?.status?.toLowerCase()
    ) {
      updateInstallmentStatus({
        variables: {
          installmentId: record.id,
          status: record.statusChanged.toLowerCase(),
        },
      })
        .then((res) => {
          refetchBooking();
          NotificationManager.success(formatMessage({ id: "success.change.installment.status" }));
        })
        .catch((e) => {
          NotificationManager.error(e.message);
        });
    }
  };

  return (
    <>
      {InstallmentData?.length ? (
        <table className="mt-2 mb-2" style={{ borderCollapse: "collapse", height: "100%" }}>
          <tr className="px-2 py-2">
            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="Due.Date" />
            </th>
            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="Amount" />
            </th>
            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="status" />
            </th>
            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="payment_method" />
            </th>
            <th className="px-2 py-2 table-bordered" style={{ minWidth: "130px" }}>
              <FormattedMessage id="Payment.Brand" />
            </th>
            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="Paid at" />
            </th>
            <th className="px-2 py-2 table-bordered" style={{ minWidth: "140px" }}>
              <FormattedMessage id="Wallet Value" />
            </th>
            <th className="px-2 py-2 table-bordered" style={{ minWidth: "140px" }}>
              <FormattedMessage id="Earning Value" />
            </th>
            <th className="px-2 py-2 table-bordered" style={{ minWidth: "140px" }}>
              <FormattedMessage id="Earn Status" />
            </th>
            <th className="px-2 py-2 table-bordered" style={{ minWidth: "140px" }}>
              <FormattedMessage id="Miles Price" />
            </th>

            <th className="px-2 py-2 table-bordered">
              <FormattedMessage id="common.actions" />
            </th>
          </tr>

          {InstallmentData &&
            InstallmentData?.map((i, index) => (
              <tr key={index}>
                <td style={{ minWidth: "120px" }} className="px-2 py-2 table-bordered">
                  <MuiPickersUtilsProvider libInstance={moment} utils={MomentUtils} locale={locale}>
                    <MaterialDateTimePicker
                      okLabel={formatMessage({ id: "ok" })}
                      cancelLabel={formatMessage({ id: "cancel" })}
                      clearLabel={formatMessage({ id: "clear" })}
                      clearable
                      style={{ width: "100%" }}
                      disabled
                      value={moment(`${i?.dueDate}`, "YYYY-MM-DD")}
                      name="dob"
                      placeholder={formatMessage({ id: "drop_off_date" })}
                      renderInput={(props) => <TextField {...props} />}
                      format="DD-MM-YYYY"
                      ampm
                    />
                  </MuiPickersUtilsProvider>
                </td>
                <td className="px-2 py-2 table-bordered">
                  {i.amount && (
                    <div
                      style={{
                        display: "flex",
                        gap: "3px",
                        alignItems: "center",
                        direction: "ltr",
                      }}
                    >
                      <RiyalComponent />
                      <span>{i.amount}</span>
                    </div>
                  )}
                </td>
                <td className="px-2 py-2 table-bordered" style={{ minWidth: "170px" }}>
                  {i.status?.toLowerCase() == "not_collected" && i.paymentMethod == "CASH" ? (
                    <Select
                      style={{ minWidth: "200px" }}
                      options={[
                        {
                          label: <FormattedMessage id="not_collected" />,
                          value: "not_collected",
                        },
                        {
                          label: <FormattedMessage id="not_collectable" />,
                          value: "not_collectable",
                        },

                        { label: <FormattedMessage id="Paid" />, value: "Paid" },
                      ]}
                      defaultValue={{
                        label: <FormattedMessage id="not_collected" />,
                        value: "not_collected",
                      }}
                      onChange={(selection) => {
                        let AllInstalments = [...InstallmentData];
                        AllInstalments[index].statusChanged = selection.value;
                      }}
                    />
                  ) : i.status?.toLowerCase() == "upcoming" && !i.paymentMethod ? (
                    <Select
                      style={{ minWidth: "200px" }}
                      options={[
                        { label: <FormattedMessage id="upcoming" />, value: "upcoming" },
                        { label: <FormattedMessage id="Paid" />, value: "Paid" },
                      ]}
                      isOptionDisabled={(option) => option.isdisabled} // disable an option
                      defaultValue={{
                        label: <FormattedMessage id="upcoming" />,
                        value: "upcoming",
                      }}
                      onChange={(selection) => {
                        let AllInstalments = [...InstallmentData];
                        AllInstalments[index].statusChanged = selection.value;
                      }}
                    />
                  ) : (i.status?.toLowerCase() == "due" && !i.paymentMethod) ||
                    (i.status?.toLowerCase() == "due" &&
                      i?.paymentMethod?.toLowerCase() == "online") ? (
                    <Select
                      style={{ minWidth: "200px" }}
                      options={[
                        { label: <FormattedMessage id="due" />, value: "due" },

                        { label: <FormattedMessage id="paid" />, value: "paid" },

                        {
                          label: <FormattedMessage id="not_collectable" />,
                          value: "not_collectable",
                        },
                      ]}
                      isOptionDisabled={(option) => option.isdisabled} // disable an option
                      defaultValue={{ label: <FormattedMessage id="due" />, value: "due" }}
                      onChange={(selection) => {
                        let AllInstalments = [...InstallmentData];
                        AllInstalments[index].statusChanged = selection.value;
                      }}
                    />
                  ) : i.status?.toLowerCase() == "paid" && i.paymentMethod == "CASH" ? (
                    <Select
                      style={{ minWidth: "200px" }}
                      options={[
                        { label: <FormattedMessage id="paid" />, value: "paid" },

                        {
                          label: <FormattedMessage id="fully_refunded" />,
                          value: "fully_refunded",
                        },

                        {
                          label: <FormattedMessage id="partially_refunded" />,
                          value: "partially_refunded",
                        },
                      ]}
                      isOptionDisabled={(option) => option.isdisabled} // disable an option
                      defaultValue={{ label: <FormattedMessage id="paid" />, value: "paid" }}
                      onChange={(selection) => {
                        let AllInstalments = [...InstallmentData];
                        AllInstalments[index].statusChanged = selection.value;
                      }}
                    />
                  ) : i.status?.toLowerCase() == "overdue" && !i.paymentMethod ? (
                    <Select
                      style={{ minWidth: "200px" }}
                      options={[
                        { label: <FormattedMessage id="overdue" />, value: "overdue" },

                        { label: <FormattedMessage id="paid" />, value: "paid" },

                        {
                          label: <FormattedMessage id="not_collected" />,
                          value: "not_collected",
                        },
                      ]}
                      isOptionDisabled={(option) => option.isdisabled} // disable an option
                      defaultValue={{
                        label: <FormattedMessage id="overdue" />,
                        value: "overdue",
                      }}
                      onChange={(selection) => {
                        let AllInstalments = [...InstallmentData];
                        AllInstalments[index].statusChanged = selection.value;
                      }}
                    />
                  ) : i?.status ? (
                    <FormattedMessage id={i.status?.toLowerCase()} />
                  ) : null}
                </td>
                <td className="px-2 py-2 table-bordered">
                  {i.paymentMethod && <FormattedMessage id={i?.paymentMethod} />}
                </td>
                <td className="px-2 py-2 table-bordered">
                  {i.paymentMethod?.toLowerCase() != "cash"
                    ? i.lastPaidInstallment?.paymentBrand
                    : ""}
                </td>
                <td className="px-2 py-2 table-bordered" style={{ minWidth: "120px" }}>
                  <MuiPickersUtilsProvider libInstance={moment} utils={MomentUtils} locale={locale}>
                    <MaterialDateTimePicker
                      okLabel={formatMessage({ id: "ok" })}
                      cancelLabel={formatMessage({ id: "cancel" })}
                      clearLabel={formatMessage({ id: "clear" })}
                      clearable
                      style={{ width: "100%" }}
                      disabled
                      value={
                        i?.totalAmountPaidAt ? moment(`${i.totalAmountPaidAt}`, "YYYY-MM-DD") : null
                      }
                      name="dob"
                      renderInput={(props) => <TextField {...props} />}
                      format="DD-MM-YYYY"
                      ampm
                    />
                  </MuiPickersUtilsProvider>
                </td>
                <td className="px-2 py-2 table-bordered">{i.walletPaidAmount}</td>

                <td className="px-2 py-2 table-bordered">
                  {i.loyaltyCollections?.[0]?.numberOfPoints}
                </td>

                <td className="px-2 py-2 table-bordered">
                  {i.loyaltyCollections?.[0]?.status ? (
                    <FormattedMessage id={i.loyaltyCollections?.[0]?.status} />
                  ) : null}
                </td>

                <td className="px-2 py-2 table-bordered">
                  {i.loyaltyCollections?.[0]?.pointPrice}
                </td>

                <td className="px-2 py-2 table-bordered">
                  <div className="d-flex" style={{ gap: "5px" }}>
                    {((i?.status?.toLowerCase() == "not_collected" && i.paymentMethod == "CASH") ||
                      (i?.status?.toLowerCase() == "upcoming" && !i.paymentMethod) ||
                      (i.status?.toLowerCase() == "due" && !i.paymentMethod) ||
                      (i?.status?.toLowerCase() == "due" &&
                        i.paymentMethod.toLowerCase() == "online") ||
                      (i?.status?.toLowerCase() == "overdue" && !i.paymentMethod) ||
                      (i?.status?.toLowerCase() == "paid" && i.paymentMethod == "CASH")) &&
                    userCan("rentals.update_status") ? (
                      <div onClick={() => updateRequestHandler(i, index)}>
                        <label
                          style={{ margin: 0, padding: 0 }}
                          title={formatMessage({ id: "Edit" })}
                        >
                          <Edit style={{ cursor: "pointer" }} />
                        </label>
                      </div>
                    ) : null}
                    {i?.paymentMethod?.toLowerCase() != "cash" &&
                    i?.status?.toLowerCase() == "paid" &&
                    bookDetails?.status?.toLowerCase() != "pending" ? (
                      <RefundInstallmentComponent refetch={refetchBooking} InstallmentDetails={i} />
                    ) : null}
                    {i.canSendToAlly && i?.installmentNumber != 1 ? (
                      <label title={formatMessage({ id: "Resend to ally" })}>
                        <SendIcon
                          onClick={() =>
                            sendInstallmentToAlly(i.id, () => {
                              refetchBooking();
                            })
                          }
                          style={{ cursor: "pointer" }}
                        />
                      </label>
                    ) : null}

                    {!ally_id &&
                    (i?.hasPendingPaymentTransaction || i?.paymentStatus === "failed") &&
                    i.paymentMethod?.toLowerCase() != "cash" ? (
                      <div
                        style={
                          (i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction) &&
                          i.paymentMethod?.toLowerCase() != "cash"
                            ? { cursor: "pointer", textDecoration: "underline" }
                            : {}
                        }
                        onClick={() => {
                          if (
                            (i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction) &&
                            i.paymentMethod?.toLowerCase() != "cash"
                          ) {
                            _recallPaymentGateway({ rentalId, installmentId: i.id }).finally(() => {
                              refetchBooking();
                            });
                          }
                        }}
                      >
                        <div
                          title={
                            (i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction) &&
                            i.paymentMethod?.toLowerCase() != "cash"
                              ? formatMessage({ id: "RecallGateway" })
                              : ""
                          }
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "4px",
                            cursor: "pointer",
                          }}
                        >
                          {(i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction) &&
                          i.paymentMethod?.toLowerCase() != "cash" ? (
                            <Refresh
                              style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }}
                            />
                          ) : null}
                          <p className="m-0">
                            {i?.hasPendingPaymentTransaction ? (
                              <FormattedMessage id="pending" />
                            ) : (
                              formatMessage({ id: "notpayed" })
                            )}
                          </p>
                        </div>
                      </div>
                    ) : null}
                    {i?.payable && (
                      <PaymentLink
                        payableId={i.id}
                        payableType="Installment"
                        hide={!i?.payable}
                        token={i.paymentLink?.token}
                        validForPayment={i.paymentLink?.validForPayment}
                      />
                    )}
                    
                  </div>
                </td>
              </tr>
            ))}
        </table>
      ) : (
        <p className="text-center my-4">
          <FormattedMessage id="No data found" />
        </p>
      )}


    </>
  );
}
