import { userCan } from "functions";
import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import { RecallPaymentGateway } from "gql/queries/RecallPaymentGateway.gql";
import PropTypes from "prop-types";
import Refund from "./Refund";
import store from "../../../../store";
import PaymentLink from "./PaymentLink";
const { ally_id } = store.getState()?.authUser.user;

const showRecall = (record) => {
  if (
    record?.hasPendingPaymentTransaction ||
    record?.mergedInstallments?.find(
      (i) => i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction,
    ) ||
    record.rentalPayments?.find(
      (i) =>
        i?.status === "failed" || i?.status === "pending" || i?.status === "pending_transaction",
    )
  ) {
    return true;
  }
  return false;
};

function handleRecall(record, _recallPaymentGateway, refetch, setLoading) {
  if (ally_id) return;
  if (showRecall(record)) {
    setLoading(true);
    if (!record?.mergedInstallments) {
      _recallPaymentGateway({ rentalId: record.id })
        .then(() => {
          refetch();
        })
        .catch((error) => {
          console.error(error.message || "Failed to recall payment");
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      _recallPaymentGateway({
        rentalId: record.id,
        installmentId: record?.mergedInstallments?.find(
          (i) => i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction,
        )?.id,
      })
        .then(() => {
          refetch();
        })
        .catch((error) => {
          console.error(error.message || "Failed to recall payment");
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }
}

function NotPayed({ record, refetch }) {
  const { refetch: _recallPaymentGateway } = useQuery(RecallPaymentGateway, { skip: true });

  const { formatMessage } = useIntl();
  const [isLoading, setIsLoading] = useState(false);

  return (
    <>
      <div
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleRecall(record, _recallPaymentGateway, refetch, setIsLoading);
          }
        }}
        style={
          !ally_id && showRecall(record) ? { cursor: "pointer", textDecoration: "underline" } : {}
        }
        onClick={() => {
          handleRecall(record, _recallPaymentGateway, refetch, setIsLoading);
        }}
      >
        <div
          title={!ally_id && showRecall(record) ? formatMessage({ id: "RecallGateway" }) : ""}
          style={{ display: "flex", alignItems: "center", gap: "4px", cursor: "pointer" }}
        >
          {showRecall(record) ? (
            <i
              className={`fa ${isLoading ? "fa-spinner fa-spin" : "fa-refresh"}`}
              aria-label={formatMessage({ id: "RecallGateway" })}
            />
          ) : null}
          <p style={{ fontSize: "13px" }}>
            {record?.paidInstallmentsCount && !record?.paidInstallmentsCount?.startsWith("0")
              ? record?.paidInstallmentsCount
              : formatMessage({ id: "notpayed" })}
            {isLoading && <span className="loading-dots">...</span>}
          </p>
        </div>
      </div>
      <div>
        <PaymentLink
          payableId={record.id}
          payableType="Rental"
          hide={!record?.payable}
          token={record?.paymentLink?.token}
          validForPayment={record?.paymentLink?.validForPayment}
        />
      </div>
    </>
  );
}

NotPayed.propTypes = {
  record: PropTypes.shape({
    id: PropTypes.string.isRequired,
    paymentMethod: PropTypes.string,
    isPaid: PropTypes.bool,
    hasPendingPaymentTransaction: PropTypes.bool,
    refundedAt: PropTypes.string,
    is24Passed: PropTypes.bool,
    withInstallment: PropTypes.bool,
    isRentToOwn: PropTypes.bool,
    payable: PropTypes.bool,
    paymentLink: PropTypes.shape({
      token: PropTypes.string,
      validForPayment: PropTypes.bool,
    }),
    installments: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        status: PropTypes.string,
        paymentStatus: PropTypes.string,
        hasPendingPaymentTransaction: PropTypes.bool,
      }),
    ),
    mergedInstallments: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        status: PropTypes.string,
        paymentStatus: PropTypes.string,
        hasPendingPaymentTransaction: PropTypes.bool,
      }),
    ),
    paidInstallmentsCount: PropTypes.number,
    refundable: PropTypes.bool,
  }).isRequired,
  refetch: PropTypes.func.isRequired,
};

const RefundBooking = ({ record, refetch, inBookingDetails }) => {
  const { refetch: _recallPaymentGateway } = useQuery(RecallPaymentGateway, { skip: true });
  const { formatMessage } = useIntl();
  const [isLoading, setIsLoading] = useState(false);

  const renderPaymentStatus = () => {
    if (record.isPaid && record.paymentMethod === "ONLINE") {
      return (
        <p style={{ fontSize: "12px" }} className="badge badge-info m-0">
          <FormattedMessage id="payed" />
        </p>
      );
    }
    if (!record.isPaid && record.paymentMethod === "ONLINE" && record.paymentMethod !== "CASH") {
      return <NotPayed {...{ refetch, record }} />;
    }
    return null;
  };

  const renderInstallmentStatus = () => {
    if (record.withInstallment || record?.isRentToOwn) {
      return (
        <div style={{ display: "flex", gap: "5px" }}>
          <div
            role="button"
            tabIndex={0}
            title={!ally_id && showRecall(record) ? formatMessage({ id: "RecallGateway" }) : ""}
            style={{ display: "flex", alignItems: "center", gap: "4px", cursor: "pointer" }}
            onClick={() => handleRecall(record, _recallPaymentGateway, refetch, setIsLoading)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                handleRecall(record, _recallPaymentGateway, refetch, setIsLoading);
              }
            }}
          >
            {showRecall(record) && <i className="fa fa-refresh" />}
            {renderInstallmentPaymentStatus()}
            {renderPartiallyRefundedStatus()}
          </div>
          {record.paidInstallmentsCount}
        </div>
      );
    }
    if (record.paymentMethod === "ONLINE" && record.hasPendingPaymentTransaction) {
      return (
        <>
          {showRecall(record) && (
            <i
              role="button"
              tabIndex={0}
              aria-label={formatMessage({ id: "RecallGateway" })}
              className="fa fa-refresh"
              style={{ cursor: "pointer" }}
              onClick={() => handleRecall(record, _recallPaymentGateway, refetch, setIsLoading)}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  handleRecall(record, _recallPaymentGateway, refetch, setIsLoading);
                }
              }}
            />
          )}
          <p className="badge badge-warning">
            <FormattedMessage id="pending" />
          </p>
        </>
      );
    }
    if (record.isPaid && record.paymentMethod === "ONLINE") {
      return (
        <>
          {!record.refundedAt && (
            <p className="badge badge-success" style={{ fontSize: "12px" }}>
              <FormattedMessage id="payed" />
            </p>
          )}
          {record.refundedAt && (
            <p className="badge badge-danger" style={{ fontSize: "12px" }}>
              <FormattedMessage id="refund.money" />
            </p>
          )}
        </>
      );
    }
    if (!record.isPaid && record.paymentMethod === "ONLINE") {
      return <NotPayed {...{ refetch, record }} />;
    }
    return null;
  };

  const renderInstallmentPaymentStatus = () => {
    if (!showRecall(record)) {
      return <FormattedMessage id="rent.payed" />;
    }
    if (record.installments?.filter((item) => item.hasPendingPaymentTransaction)?.length) {
      return (
        <p className="badge badge-warning">
          <FormattedMessage id="pending" />
        </p>
      );
    }
    return <FormattedMessage id="notpayed" />;
  };

  const renderPartiallyRefundedStatus = () => {
    if (
      record.installments?.filter(
        (item) => item.status === "partially_refunded" || item.status === "fully_refunded",
      )?.length
    ) {
      return (
        <p style={{ fontSize: "12px" }} className="badge badge-warning m-0">
          <FormattedMessage id="partially_refunded" />
        </p>
      );
    }
    return null;
  };

  if (!userCan("rentals.refund") || !record?.refundable) {
    return (
      <div style={{ textAlign: "center", display: "flex", gap: "7px", alignItems: "center" }}>
        {!inBookingDetails ? (
          <p style={{ fontSize: "13px" }}>
            <FormattedMessage id={record.paymentMethod} />
          </p>
        ) : null}
        {renderInstallmentStatus()}
      </div>
    );
  }

  return (
    <div
      style={{
        textAlign: "center",
        display: "flex",
        gap: "4px",
        alignItems: "center",
        minHeight: "24px",
      }}
    >
      {!inBookingDetails ? (
        <p
          style={{
            fontSize: "13px",
            margin: 0,
            display: "flex",
            alignItems: "center",
          }}
        >
          <FormattedMessage id={record.paymentMethod} />
        </p>
      ) : null}

      {renderPaymentStatus()}
      {!ally_id && (
        <Refund
          rentalid={record.id}
          is24Passed={record.is24Passed}
          refetch={refetch}
          bookingDetails={record}
        />
      )}
    </div>
  );
};

RefundBooking.propTypes = {
  record: PropTypes.shape({
    id: PropTypes.string.isRequired,
    paymentMethod: PropTypes.string,
    isPaid: PropTypes.bool,
    hasPendingPaymentTransaction: PropTypes.bool,
    refundedAt: PropTypes.string,
    is24Passed: PropTypes.bool,
    withInstallment: PropTypes.bool,
    isRentToOwn: PropTypes.bool,
    mergedInstallments: PropTypes.array,
    paymentLink: PropTypes.shape({
      token: PropTypes.string,
      validForPayment: PropTypes.bool,
    }),
    installments: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        status: PropTypes.string,
        paymentStatus: PropTypes.string,
        hasPendingPaymentTransaction: PropTypes.bool,
      }),
    ),
    paidInstallmentsCount: PropTypes.number,
    refundable: PropTypes.bool,
  }).isRequired,
  refetch: PropTypes.func.isRequired,
};

export default RefundBooking;
