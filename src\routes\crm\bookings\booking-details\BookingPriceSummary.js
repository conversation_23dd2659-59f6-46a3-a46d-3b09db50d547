/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
import React from "react";
import PropTypes from "prop-types";
import { ListItem, CircularProgress } from "@material-ui/core";
import { FormattedMessage, useIntl } from "react-intl";
import RiyalComponent from "components/shared/RiyalComponent";
import store from "../../../../store";

function BookingPriceSummary({
  BookingPrice,
  calculatingPrice,
  insurance,
  isHandover,
  BookingDetails,
  isUnlimited,
  inBookingDetails,
  bookingType,
  plan,
  change = false,
  _handoverPrice,
  deliveryType,
  insuranceName,
}) {
  const aboutPrice = BookingPrice?.aboutRentPrice || {};

  const {
    totalAddsPrice,
    deliveryPrice,
    discountPercentage,
    discountType,
    discountValue,
    insuranceIncluded,
    insuranceValue,
    numberOfDays,
    priceBeforeDiscount,
    priceBeforeInsurance,
    pricePerDay,
    dailyPrice,
    taxValue,
    totalPrice,
    valueAddedTaxPercentage,
    priceBeforeTax,
    handoverPrice,
    couponDiscount,
    couponCode,
    branchExtraServices,
    allyExtraServices,
    totalUnlimitedFee,
    totalAmountDue,
    remainingDueInstallmentsAmount,
    completedInstallmentsAmount,
    rentToOwnInstallmentBreakdown,
    insuranceName: insuranceNameAboutPrice,
    addsPrice,
    residualAmount: residualAmountAboutPrice,
  } = aboutPrice;
  const { is_super_user } = store.getState()?.authUser.user;

  const residualAmount =
    residualAmountAboutPrice || BookingDetails?.rentalDetails?.residualAmount || 0;
  const { locale, formatMessage } = useIntl();
  const totalBeforeInsurance = [
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="1st installment" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan?.firstInstallment
                  : rentToOwnInstallmentBreakdown?.firstPayment}
              </span>
            </div>
          ),
        }
      : {
          msg: <FormattedMessage id="aboutPrice.PricePerDay" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{!change ? BookingDetails?.rentalDetails?.dailyPrice : dailyPrice}</span>
            </div>
          ),
        },
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Monthly installment" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan
                      ?.monthlyInstallment
                  : rentToOwnInstallmentBreakdown?.monthlyInstallment}
              </span>
            </div>
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Final Installment" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan?.finalInstallment
                  : rentToOwnInstallmentBreakdown?.finalInstallment}
              </span>
            </div>
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="No. of months" />,
          value: plan?.noOfMonths,
        }
      : {
          msg: (
            <FormattedMessage
              id="aboutPrice.totalDays"
              values={{
                days: `${!change ? BookingDetails?.rentalDetails?.numberOfDays : numberOfDays}`,
              }}
            />
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change ? BookingDetails?.rentalDetails?.priceBeforeDiscount : priceBeforeDiscount}
              </span>
            </div>
          ),
        },
    BookingDetails?.rentalDetails?.discountValue || discountValue
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.discount"
              values={{
                discount:
                  !change && BookingDetails?.rentalDetails?.discountType
                    ? BookingDetails?.rentalDetails?.discountType
                    : discountType
                    ? `${discountType}`
                    : null,
                percentage: `${
                  BookingDetails?.rentalDetails?.discountPercentage || discountPercentage
                }`,
              }}
            />
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{BookingDetails?.rentalDetails?.discountValue || discountValue}</span>
            </div>
          ),
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.couponCode
        ? {
            msg: <FormattedMessage id="code.label" />,
            value: BookingDetails?.rentalDetails?.couponCode,
          }
        : null
      : couponCode
      ? {
          msg: <FormattedMessage id="code.label" />,
          value: couponCode,
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.couponCode
        ? {
            msg: <FormattedMessage id="couponDiscount" />,
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{BookingDetails?.rentalDetails?.couponDiscount}</span>
              </div>
            ),
          }
        : null
      : couponCode
      ? {
          msg: <FormattedMessage id="couponDiscount" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{couponDiscount}</span>
            </div>
          ),
        }
      : null,

    {
      msg: "",
      value: (
        <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
          <RiyalComponent />
          <span>
            {!change ? BookingDetails?.rentalDetails?.priceBeforeInsurance : priceBeforeInsurance}
          </span>
        </div>
      ),
    },
  ];

  const extras = [
    isHandover
      ? {
          msg: <FormattedMessage id="handoverprice" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.handoverPrice
                  : _handoverPrice || handoverPrice}
              </span>
            </div>
          ),
        }
      : null,

    !change
      ? BookingDetails?.rentalDetails?.insuranceIncluded
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.insurance"
                values={{
                  insurance:
                    BookingDetails?.rentalDetails?.insuranceName ||
                    insuranceName ||
                    insuranceNameAboutPrice ||
                    "",
                }}
              />
            ),
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{BookingDetails?.rentalDetails?.totalInsurancePrice}</span>
              </div>
            ),
          }
        : null
      : insuranceIncluded
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.insurance"
              values={{ insurance: insurance.insuranceName || insuranceName }}
            />
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{insuranceValue}</span>
            </div>
          ),
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.rentalExtraServices?.length
        ? BookingDetails?.rentalDetails?.rentalExtraServices?.map((service) => ({
            msg: service[`${locale}Title`],
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{service?.totalServiceValue}</span>
              </div>
            ),
          }))
        : insuranceIncluded
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.insurance"
                values={{ insurance: insurance.insuranceName || insuranceName }}
              />
            ),
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{insuranceValue}</span>
              </div>
            ),
          }
        : change && insuranceIncluded
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.insurance"
                values={{ insurance: insurance.insuranceName || insuranceName }}
              />
            ),
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{insuranceValue}</span>
              </div>
            ),
          }
        : null
      : branchExtraServices?.length
      ? branchExtraServices?.map((service) => ({
          msg: service[`${locale}Title`] || <FormattedMessage id="aboutPrice.extraServices" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{service?.totalServiceValue}</span>
            </div>
          ),
        }))
      : null,
    allyExtraServices?.length
      ? allyExtraServices?.map((service) => ({
          msg: service[`${locale}Title`] || <FormattedMessage id="aboutPrice.extraServices" />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{service?.totalServiceValue}</span>
            </div>
          ),
        }))
      : null,
    (!change && BookingDetails?.rentalDetails?.deliveryPrice) || deliveryPrice
      ? {
          msg: `${formatMessage({ id: "Car_Delivery" })} (${formatMessage({ id: deliveryType })})`,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{!change ? BookingDetails?.rentalDetails?.deliveryPrice : deliveryPrice}</span>
            </div>
          ),
        }
      : null,
    isUnlimited
      ? {
          msg: <FormattedMessage id="Unlimited.KM" />,
          value:
            totalUnlimitedFee == 0 ? (
              <FormattedMessage id="free" />
            ) : (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>
                  {change ? totalUnlimitedFee : BookingDetails?.rentalDetails?.totalUnlimitedFee}
                </span>
              </div>
            ),
        }
      : null,

    {
      msg: "",
      value: (
        <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
          <RiyalComponent />
          <span>
            {change
              ? addsPrice
              : BookingDetails?.rentalDetails?.totalAddsPrice ||
                BookingDetails?.rentalDetails?.addsPrice
              ? BookingDetails?.rentalDetails?.totalAddsPrice ||
                BookingDetails?.rentalDetails?.addsPrice
              : addsPrice}
          </span>
        </div>
      ),
    },
  ];

  const vatAndTotal = [
    !change
      ? BookingDetails?.rentalDetails?.valueAddedTaxPercentage
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.total"
                values={{ vat: BookingDetails?.rentalDetails?.valueAddedTaxPercentage }}
              />
            ),
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{BookingDetails?.rentalDetails?.priceBeforeTax}</span>
              </div>
            ),
          }
        : null
      : valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.total" values={{ vat: valueAddedTaxPercentage }} />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{priceBeforeTax}</span>
            </div>
          ),
        }
      : null,
    !change
      ? BookingDetails?.rentalDetails?.valueAddedTaxPercentage
        ? {
            msg: (
              <FormattedMessage
                id="aboutPrice.vat"
                values={{
                  vat:
                    BookingDetails?.rentalDetails?.valueAddedTaxPercentage ||
                    valueAddedTaxPercentage,
                }}
              />
            ),
            value: (
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>{BookingDetails?.rentalDetails?.taxValue}</span>
              </div>
            ),
          }
        : null
      : valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.vat" values={{ vat: valueAddedTaxPercentage }} />,
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{!change ? BookingDetails?.rentalDetails?.taxValue : taxValue}</span>
            </div>
          ),
        }
      : null,
    {
      msg:
        BookingDetails?.rentalDetails.walletTransactions ||
        BookingDetails?.rentalDetails?.rentalDateExtensionRequests?.filter(
          (item) => item?.withWallet,
        )?.length ||
        (BookingDetails?.rentalDetails?.mergedInstallments &&
          BookingDetails?.rentalDetails?.mergedInstallments.length) ? (
          <>
            <FormattedMessage id="aboutPrice.grandTotal" />{" "}
            <FormattedMessage id="aboutPrice.+vat" />
          </>
        ) : (
          <FormattedMessage id="Due Amount" />
        ),
      value: (
        <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
          <RiyalComponent />
          <span>{!change ? BookingDetails?.rentalDetails?.totalBookingPrice : totalPrice}</span>
        </div>
      ),
    },
    BookingDetails?.rentalDetails?.mergedInstallments &&
    BookingDetails?.rentalDetails?.mergedInstallments.length
      ? {
          msg: (
            <>
              <FormattedMessage id="Completed.Payments" values={{ vat: valueAddedTaxPercentage }} />{" "}
              ({BookingDetails?.rentalDetails?.paidInstallmentsCount})
            </>
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.completedInstallmentsAmount
                  : completedInstallmentsAmount}
              </span>
            </div>
          ),
        }
      : null,
    // Remaining Due

    BookingDetails?.rentalDetails.totalWalletPaidAmount
      ? {
          msg: (
            <>
              <FormattedMessage id="wallet" />
            </>
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{BookingDetails?.rentalDetails.totalWalletPaidAmount}</span>
            </div>
          ),
        }
      : null,

    BookingDetails?.rentalDetails?.rentalDateExtensionRequests?.filter((item) => item?.withWallet)
      ?.length || BookingDetails?.rentalDetails.walletTransactions
      ? {
          msg: (
            <>
              <FormattedMessage id="Due Amount" />
            </>
          ),
          value: (
            <>
              <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
                <RiyalComponent />
                <span>
                  {!change ? BookingDetails?.rentalDetails?.totalAmountDue : totalAmountDue}
                </span>
              </div>
            </>
          ),
        }
      : null,

    BookingDetails?.rentalDetails?.mergedInstallments &&
    BookingDetails?.rentalDetails?.mergedInstallments.length
      ? {
          msg: (
            <>
              <FormattedMessage id="Remaining.Due" />
            </>
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>
                {!change
                  ? BookingDetails?.rentalDetails?.remainingDueInstallmentsAmount
                  : remainingDueInstallmentsAmount}
              </span>
            </div>
          ),
        }
      : null,
    residualAmount && is_super_user
      ? {
          msg: (
            <>
              <FormattedMessage id="Residual Amount" />
            </>
          ),
          value: (
            <div style={{ display: "flex", gap: "3px", alignItems: "center", direction: "ltr" }}>
              <RiyalComponent />
              <span>{Number(residualAmount).toFixed(2)}</span>
            </div>
          ),
        }
      : null,
    BookingDetails?.rentalDetails?.refundedAmount
      ? {
          msg:
            locale == "en" ? (
              <>
                <span className="text-info">
                  {BookingDetails?.rentalDetails?.refundedAmount} SAR has been refunded to your
                  account
                </span>
              </>
            ) : (
              <span className="text-info">
                تم إيداع قيمة {BookingDetails?.rentalDetails?.refundedAmount} ريال لحساب بطاقتكم
              </span>
            ),
          value: "",
        }
      : null,
  ];
  function DataDisplay(i, row) {
    return (
      <ListItem
        key={JSON.stringify(i)}
        data-testid={`data-info-${i}`}
        className="d-flex justify-content-between align-items-center p-20"
      >
        {row?.msg ? <span>{row?.msg}</span> : <hr />}
        <span>{row?.value}</span>
      </ListItem>
    );
  }
  return BookingPrice || calculatingPrice || !change ? (
    <>
      {calculatingPrice && (
        <CircularProgress variant="determinate" size={40} thickness={4} value={100} />
      )}
      {!inBookingDetails && (
        <h3>
          <FormattedMessage id="aboutPrice" />
        </h3>
      )}

      <div className="alert-secondary">
        <h5>
          <FormattedMessage id="aboutPrice.Basic" />
        </h5>

        {totalBeforeInsurance.map((row, i) => row !== null && DataDisplay(i, row))}
        {totalAddsPrice > 0 ||
        BookingDetails?.rentalDetails?.totalAddsPrice ||
        BookingDetails?.rentalDetails?.addsPrice ||
        addsPrice ? (
          <>
            <h5>
              <FormattedMessage id="aboutPrice.extraServices" />
            </h5>
            {extras.length
              ? extras.flat(10)?.map((row, i) => row !== null && DataDisplay(i, row))
              : null}
          </>
        ) : null}

        {
          <>
            {/* <h5>
          <FormattedMessage id="aboutPrice.total" /> سس
        </h5> */}
            {vatAndTotal.map((row, i) => row !== null && DataDisplay(i, row))}
          </>
        }
      </div>
    </>
  ) : null;
}

BookingPriceSummary.propTypes = {
  BookingPrice: PropTypes.object,
  insurance: PropTypes.object,
  calculatingPrice: PropTypes.bool,
};

export default BookingPriceSummary;
