mutation CreateAgency(
  $agencyKey: String!
  $allyCompanyIds: [ID!]
  $email: String!
  $isActive: Boolean
  $logo: String
  $nameAr: String!
  $nameEn: String!
  $phoneNumber: String!
  $superAdminEmail: String!
  $superAdminFirstName: String!
  $superAdminLastName: String!
  $superAdminPassword: String!
  $superAdminPhoneNumber: String!
  $vatRegistrationId: String!
) {
  createAgency(
   input:{
     agencyKey: $agencyKey
    allyCompanyIds: $allyCompanyIds
    email: $email
    isActive: $isActive
    logo: $logo
    nameAr: $nameAr
    nameEn: $nameEn
    phoneNumber: $phoneNumber
    superAdminEmail: $superAdminEmail
    superAdminFirstName: $superAdminFirstName
    superAdminLastName: $superAdminLastName
    superAdminPassword: $superAdminPassword
    superAdminPhoneNumber: $superAdminPhoneNumber
    vatRegistrationId: $vatRegistrationId
   }
  ) {
    agency {
      id
    }
    errors
    status
  }
}
mutation UpdateAgency(
  $agencyId: ID!
  $allyCompanyIds: [ID!]
  $email: String
  $isActive: Boolean
  $logo: String
  $nameAr: String
  $nameEn: String
  $phoneNumber: String
  $vatRegistrationId: String
) {
  updateAgency(
   input:{
     agencyId: $agencyId
    allyCompanyIds: $allyCompanyIds
    email: $email
    isActive: $isActive
    logo: $logo
    nameAr: $nameAr
    nameEn: $nameEn
    phoneNumber: $phoneNumber
    vatRegistrationId: $vatRegistrationId
   }
  ) {
    errors
    status
  }
}
mutation CreateAgencyUser($input: CreateAgencyUserInput!) {
  createAgencyUser(input: $input) {
    errors
    status
  }
}

mutation EditAgencyUser($input: UpdateAgencyUserInput!) {
  updateAgencyUser(input: $input) {
    errors
    status
  }
}

mutation CreateAgencyCustomer(
  $agencyId: ID!
  $businessCard: String
  $clientMutationId: String
  $companyName: String
  $cityId: ID
  $dob: String
  # Driver license number
  $driverLicense: String
  $driverLicenseExpireAt: String
  $email: String
  $mobile: String!
  $firstName: String
  $gender: String
  $isActive: Boolean!
  $lastName: String
  $licenseFrontImage: String
  $licenseSelfieImage: String
  $middleName: String
  $nationalIdExpireAt: String
  $nationalIdVersion: Int
  $nationalityId: ID
  $nid: String
  $passportExpireAt: String
  # IMAGE
  $passportFrontImage: String
  $passportNumber: String
  # Can be visitor or citizen
  $status: String
  $borderNumber: String
  # Can be mr, mrs, ms or miss
  $profileImage: String
  # customerClass Can be [basic_member,bronze_member, gold_member, private_member]
  # $customerClass: String
  # $blockingStatus: BlockingStatus
  # $blockingAllies: [ID!]
  # $blockingReason: String
  $nidImage: String
  $visaImage: String
) {
  addAgencyCustomer(
    input: {
      agencyId: $agencyId
      businessCard: $businessCard
      clientMutationId: $clientMutationId
      companyName: $companyName
      cityId: $cityId
      dob: $dob
      driverLicense: $driverLicense
      driverLicenseExpireAt: $driverLicenseExpireAt
      email: $email
      mobile: $mobile
      firstName: $firstName
      gender: $gender
      isActive: $isActive
      lastName: $lastName
      licenseFrontImage: $licenseFrontImage
      profileImage: $profileImage
      licenseSelfieImage: $licenseSelfieImage
      middleName: $middleName
      nationalIdExpireAt: $nationalIdExpireAt
      nationalIdVersion: $nationalIdVersion
      nationalityId: $nationalityId
      nid: $nid
      passportExpireAt: $passportExpireAt
      passportFrontImage: $passportFrontImage
      passportNumber: $passportNumber
      status: $status
      borderNumber: $borderNumber
      # customerClass: $customerClass
      # blockingStatus: $blockingStatus
      # blockingAllies: $blockingAllies
      # blockingReason: $blockingReason
      nidImage: $nidImage
      visaImage: $visaImage
    }
  ) {
    clientMutationId
    errors
    status
    user {
      id
    }
  }
}

mutation CreateAgencyCustomer($input: AddAgencyCustomerInput!) {
  addAgencyCustomer(input: $input) {
    errors
    status
  }
}
mutation AgencyChargeWallet($agencyId: ID!, $amount: Float!, $type: TransactionType!) {
  agencyChargeWallet(agencyId: $agencyId, amount: $amount, type: $type) {
    wallet {
      id
    }
  }
}

# mutation EditAgencyCustomer($input: UpdateAgencyUserInput!) {
#   updateAgencyUser(input: $input) {
#     errors
#     status
#   }
# }
