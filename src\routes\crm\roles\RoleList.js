/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/**
 * Bookings List
 */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import { useMutation } from "@apollo/client";

import PropTypes from "prop-types";
import { Typography, Tooltip } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import { DeleteRole } from "gql/mutations/DeleteRole.gql";
import { NotificationManager } from "react-notifications";
import swal from "sweetalert";
import { Delete, Edit } from "@material-ui/icons";
import RolesTimeLine from "./RolesTimeLine";
import TotalResults from "components/shared/TotalResults";
import { RoleData } from "./RoleData";

function RoleList({ allRoles, loading, setPage, limit, setLimit, refetch }) {
  const history = useHistory();
  const { formatMessage } = useIntl();
  const [deleteRole] = useMutation(DeleteRole);
  const [roleId, setRoleId] = useState();
  const [bookingsState, setBookingsState] = useSetState({
    collection: [],
    metadata: {},
  });
  const [openTimelineModal, setOpenTimeLineModal] = useState(false);

  const { collection, metadata } = bookingsState;

  useEffect(() => {
    setBookingsState({
      collection: allRoles?.roles?.collection,
      metadata: allRoles?.roles?.metadata,
    });
  }, [allRoles]);
  const handelDeleteRole = (id) => {
    swal({
      title: formatMessage({ id: "are.u.sure.?" }),
      text: formatMessage({ id: "u.want.delete.role" }),
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "delete" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        deleteRole({
          variables: {
            input: { id: +id },
          },
        })
          .then(() => refetch())
          .then((res) =>
            swal(formatMessage({ id: "roledeletedsuccessfully" }), {
              icon: "success",
            }),
          )
          .catch((error) => {
            NotificationManager.error(`${error?.message}`);
          });
      }
    });
  };
  const getRoleAudit = (id) => {
    setRoleId(id);
    setOpenTimeLineModal(true);
  };
  const actions = ({ id }) => (
    <div className="d-flex align-items-center" style={{ gap: "5px" }}>
      <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
        <Link to={`roles/${id}/edit`}>
          <Edit style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }} />
        </Link>
      </Tooltip>
      <Tooltip title={formatMessage({ id: "common.delete" })} placement="top">
        <Delete
          style={{ cursor: "pointer", fontSize: "18px", color: "#5d89d8" }}
          onClick={() => handelDeleteRole(id)}
        />
      </Tooltip>
      <Tooltip title={formatMessage({ id: "common.timeline" })} placement="top">
        <Link>
          <i className="fas fa-history" onClick={() => getRoleAudit(id)}></i>
        </Link>
      </Tooltip>
    </div>
  );
  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={RoleData}
            loading={loading}
            tableRecords={collection}
            actions={actions}
            actionsArgs={["id"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => setLimit(value)}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>

      <RolesTimeLine
        RoleId={roleId}
        isOpen={openTimelineModal}
        setOpenTimeLineModal={setOpenTimeLineModal}
      />
    </Typography>
  );
}

RoleList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  loading: PropTypes.bool,
  allRoles: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default RoleList;
