import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>eader, <PERSON>dalBody, ModalFooter } from "reactstrap";
import { useQuery } from "@apollo/client";
import moment from "moment";
import {MakeAudits} from "gql/queries/makesAudit.gql"
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import "./style.css";
// import { JSONParser } from "@amcharts/amcharts4/core";
const MakeTimeLine = (props) => {
  const { locale } = useIntl();

  const { className } = props;
  const [oldData, setOldData] = useState();
  const [newData, setNewData] = useState();
  const { data: makeAudit, refetch } = useQuery(MakeAudits, {
    skip: !props.MakeId,
    variables: { id:props?.MakeId},
  });
  useEffect(() => {
    if (makeAudit?.makeAudits?.length) {
      const newData = [];
      const oldData = [];
      makeAudit?.makeAudits?.map((item, i) => {
        newData.push(item?.newData);
        oldData.push(item?.oldData);
      });
      setOldData(oldData);
      setNewData(newData);
    }
  }, [makeAudit]);
  useEffect(() => {
    if (!props.MakeId) {
      return;
    }
    refetch();
  }, [props.isOpen]);
  const toggle = () => props.setOpenTimeLineModal(!props.isOpen);

  return (
    <Modal isOpen={props.isOpen} toggle={toggle} className={className}>
      <ModalHeader toggle={toggle}>
        <FormattedMessage id="MakeTimeLine" />
      </ModalHeader>
      <ModalBody>
        {makeAudit?.makeAudits?.length ? (
          <div class="container" style={{ height: "500px", overflowY: "auto" }}>
            <div class="row">
              <div class="col-md-10">
                <ul class="cbp_tmtimeline" style={{direction: locale =="ar" ? "ltr" : ""}}>
                  <li style={{width:"90%"}}>
                    <div class="cbp_tmicon">
                      <i class="zmdi zmdi-account"></i>
                    </div>
                    <div class="cbp_tmlabel empty">
                      {" "}
                      <span style={{ fontWeight: "bold" }}>
                        <FormattedMessage id="MakeId" /> :
                        {props?.MakeId}
                      </span>{" "}
                    </div>
                  </li>

                  {makeAudit?.makeAudits.map((rental, index) => (
                    <li style={{width:"90%"}}>

                      <time
                        class="cbp_tmtime"
                        style={{ left: locale == "ar" ? "1px" : "" }}
                        datetime=""
                      >
                        <span>{rental.userName}</span>{" "}
                        <span style={{ direction: "ltr" }}>
                          { rental.createdAt && moment.utc(rental.createdAt).local().format("DD/MM/YYYY h:mm:ss a")}
                        </span>
                        {/* <span>{JSON.parse(rental.userRole).map((role)=>role)}</span> */}

                      </time>
                      <div class="cbp_tmicon bg-info">
                        <i class="zmdi zmdi-label"></i>
                      </div>
                       <div className="cbp_tmlabel">
                        {
                           rental?.referenceNo ? 
                           <div style={{direction:"rtl"}}>
                           
                             <span>
                                <FormattedMessage id="Extension.id" /> : 
                                { rental?.referenceNo}

                             </span>  
                           
                                
   
                           </div>
                           : null
                        }
                     
                      <div class=" d-flex" style={{ justifyContent: "space-between" }}>
                          
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="oldData" />
                          </h2>
                          <ul>
                          
                            {oldData &&
                              oldData[index] &&
                              Object.entries(oldData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? `${key}` : "0"} />}
                                    {": "}
                                    {val &&
                                    val != null &&
                                    (key === "pick_up_time" || key === "drop_off_time" || key == "refunded_at")
                                  
                                      ?                                       
                                      moment( oldData[index][key], 'HHmmss').format('HH:mm:ss a')
                                     
                                     
                                      :
                                      key == "status" ?
                                      val ? 
                                      <CheckIcon style={{color:"green",verticalAlign:"middle"}}/>
                                      :  
                                   
                                      <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                      : 
                                      key == "decline_reason" ?
                                      <>
                                        <ul style={{paddingInline:"10px"}}>
                                         {
                                          val?.split(",").map((value)=>(
                                            <li> {value}</li>
                                          ))
                                         } 
                                        </ul>
                                      </>
                                      :
                                      val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{"  "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="newData" />
                          </h2>
                          <ul>
                            {newData &&
                              newData[index] &&
                              Object.entries(newData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? key : "0"} />} :{" "}
                                    {val &&
                                    val != null &&
                                    (key === "pick_up_time" || key === "drop_off_time" || key === "refunded_at")
                                      
                                      ?
                                      moment( newData[index][key], 'HHmmss').format('HH:mm:ss a') 
                                      :
                                      key == "decline_reason" ?
                                      <>
                                        <ul style={{paddingInline:"10px"}}>
                                         {
                                          val?.split(",").map((value)=>(
                                            <li> {value}</li>
                                          ))
                                         } 
                                        </ul>
                                      </>
                                        : key == "status" ?
                                         val ? 
                                         <CheckIcon style={{color:"green",verticalAlign:"middle"}}/>
                                         :  
                                      
                                         <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                      :
                                       val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{" "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                      </div>
                      </div>

                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ) : (
          <div className="d-flex" style={{ justifyContent: "center" }}>
            <FormattedMessage id="No data found" />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>
          <FormattedMessage id="close" />
        </Button>
      </ModalFooter>
    </Modal>
  );
};
export default MakeTimeLine;
