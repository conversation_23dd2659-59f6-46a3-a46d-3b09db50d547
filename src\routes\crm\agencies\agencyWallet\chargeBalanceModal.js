import { useMutation } from "@apollo/client";
import { Box, FormControlLabel, Input, Radio } from "@material-ui/core";
import FormControlLabelContainer from "components/shared/FormControlLabelContainer";
import RadioGroupContainer from "components/shared/containers/RadioGroupContainer";
import React, { useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Modal, ModalHeader, ModalBody, ModalFooter } from "reactstrap";
import { AgencyChargeWallet } from "gql/mutations/Agencies.gql";
import { useParams } from "react-router-dom";
import { NotificationManager } from "react-notifications";
function ChargeBalanceModal({ isModalOpen, setIsModalOpen, refetch }) {
  const { formatMessage } = useIntl();
  const [chargeWallet] = useMutation(AgencyChargeWallet);
  const [errors, setErrors] = useState({ min: true });
  const [validating, setValidating] = useState(false);
  function toggle() {
    setIsModalOpen(false);
  }
  const { agencyId } = useParams();
  const [type, setType] = useState("ADDITION");
  const [amount, setAmount] = useState("ADDITION");
  function chargeWalletHandler() {
    setValidating(true);
    if (!amount) setErrors({ ...errors, min: true });
    if (Object.values(errors)?.includes(true)) {
      return;
    }
    chargeWallet({ variables: { type, amount: +amount, agencyId } })
      .then((res) => {
        if (res?.errors) {
          res.errors.map(({ message }) => {
            NotificationManager.error(message);
          });
          return;
        }
        NotificationManager.success(<FormattedMessage id={"AddSuccessfully"} />);
        refetch();
        setIsModalOpen(false);
      })
      .catch((error) => NotificationManager.error(error.message));
  }
  return (
    <Modal isOpen={isModalOpen} toggle={toggle}>
      <ModalHeader toggle={toggle}>{formatMessage({ id: "Charge Wallet" })}</ModalHeader>
      <ModalBody>
        <Box display={"flex"} flexDirection={"column"}>
          <FormControlLabelContainer labelId={formatMessage({ id: "Type" })}>
            <RadioGroupContainer value={type}>
              <FormControlLabel
                value={type.toUpperCase()}
                control={<Radio color="primary" />}
                checked={type === "ADDITION"}
                label={formatMessage({ id: "add" })}
                className="m-0"
                onChange={(e) => {
                  setType("ADDITION");
                }}
              />
              <FormControlLabel
                value={type.toUpperCase()}
                control={<Radio color="primary" />}
                checked={type === "DEDUCTION"}
                label={formatMessage({ id: "deduct" })}
                className="m-0"
                onChange={(e) => {
                  setType("DEDUCTION");
                }}
              />
            </RadioGroupContainer>
          </FormControlLabelContainer>
          <input
            style={{ borderRadius: "5px", padding: "5px 8px" }}
            placeholder={formatMessage({ id: "value" })}
            type="number"
            step="0.1"
            max={999999.99}
            min={1}
            onChange={(e) => {
              if (!e.target.value) {
                setErrors({ ...errors, min: true });
              }
              if (e.target.value > 999999.99) {
                setErrors({ ...errors, max: true, min: false });
              } else {
                setErrors({ ...errors, max: false, min: false });
              }
              setAmount(e.target.value);
            }}
          />
          {validating && errors?.min ? (
            <span className="error">
              <FormattedMessage id="thisfieldisrequired" />
            </span>
          ) : null}
          {validating && errors?.max ? (
            <span className="error">
              <FormattedMessage id="maxout.99" />
            </span>
          ) : null}
        </Box>
      </ModalBody>
      <ModalFooter>
        <Box display={"flex"} gridGap={"10px"}>
          <Button color="success" onClick={chargeWalletHandler}>
            <FormattedMessage id="ok" />
          </Button>
          <Button color="secondary" onClick={toggle}>
            <FormattedMessage id="close" />
          </Button>
        </Box>
      </ModalFooter>
    </Modal>
  );
}

export default ChargeBalanceModal;
