/* eslint-disable prettier/prettier */
import React from "react";
import { dataTypes } from "constants/constants";
import { Link } from "react-router-dom";
import moment from "moment"

const { TEXT, ACTIONS, FUNC } = dataTypes;
export const UserData = [
  {
    headerId: "user.name",
    dataType: FUNC,
    func: (record) => record.name,
  },
 

 
  {
    headerId: "phoneNumber",
    dataRef: "phoneNumber",
    dataType: FUNC,
    func: (record) => record.user.mobile,
  },
  {
    headerId: "email",
    dataRef: "email",
    dataType: TEXT,
  },
  {
    headerId: "user.role",
    dataRef: "userTypeLocalized",
    dataType: TEXT,
  },
  {
    headerId: "status",
    dataType: FUNC,
    func: (record) => (record.isActive ? "Active" : "inactive"),
  },
 

  { headerId: "common.actions", dataType: ACTIONS },
];
