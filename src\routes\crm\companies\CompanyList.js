/* eslint-disable prettier/prettier */
/**
 * Bookings List
 */
import React, { useEffect, useState } from "react";
import { useIntl, FormattedMessage } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typography, Tooltip } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import TotalResults from "components/shared/TotalResults";
import { useMutation } from "@apollo/client";
import { ActivateAllyCompany } from "gql/mutations/ActivateAlly.gql";
import Switch from "@material-ui/core/Switch";
import { NotificationManager } from "react-notifications";
import { userCan } from "functions/userCan";
import { CompanyData } from "./CompanyData";
import CompanyTimeLine from "./CompanyTimeLine";
function CompanyList({ allyCompanies, loading, setPage, limit, setLimit, refetch }) {
  const history = useHistory();
  const { formatMessage } = useIntl();

  const [allyCompany, setAllyCompany] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = allyCompany;
  const [activateAllyCompany, { loading: ActivateLoader }] = useMutation(ActivateAllyCompany);
  useEffect(() => {
    setAllyCompany({
      collection: allyCompanies?.allyCompanies?.collection,
      metadata: allyCompanies?.allyCompanies?.metadata,
    });
  }, [allyCompanies]);
  const handleChange = (e, id) => {
    activateAllyCompany({
      variables: {
        allyCompanyId: id,
        isActive: e.target.checked,
      },
    })
      .then((res) => refetch())
      .then(() => NotificationManager.success(<FormattedMessage id="StatusSucessfully" />))
      .catch((err) => {
        NotificationManager.erro(err?.message);
      });
  };
  const [openTimelineModal, setOpenTimeLineModal] = useState(false);
  const [companyId, setCompanyId] = useState();
  const getCompanyAudits = (id) => {
    setCompanyId(id);
    setOpenTimeLineModal(true);
  };
  const actions = ({ id, isActive }) => (
    <div className="d-flex align-items-center">
      <Switch
        checked={isActive}
        color="primary"
        disabled={ActivateLoader}
        name={id}
        onChange={(e) => handleChange(e, id)}
        inputProps={{ "aria-label": "primary checkbox" }}
      />
      {userCan("ally_companies.update") && (
        <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
          <Link to={`companies/${id}/edit`}>
            <i className=" ti-pencil-alt m-1"></i>
          </Link>
        </Tooltip>
      )}
      <Tooltip title={formatMessage({ id: "common.timeline" })} placement="top">
        <Link>
          <i className="fas fa-history" onClick={() => getCompanyAudits(id)}></i>
        </Link>
      </Tooltip>
    </div>
  );
  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={CompanyData}
            loading={loading}
            tableRecords={collection}
            actions={actions}
            actionsArgs={["id", "isActive"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => setLimit(value)}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>
      <CompanyTimeLine
        CompanyId={companyId}
        isOpen={openTimelineModal}
        setOpenTimeLineModal={setOpenTimeLineModal}
      />
    </Typography>
  );
}

CompanyList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  refetch: PropTypes.func,
  loading: PropTypes.bool,
  allyCompanies: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default CompanyList;
