/** Bookings List */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typo<PERSON>, Tooltip } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import TotalResults from "components/shared/TotalResults";
import Switch from "@material-ui/core/Switch";
import { useMutation } from "@apollo/client";
import { DeleteBanner } from "gql/mutations/DeleteBanner.gql";
import NotificationManager from "react-notifications/lib/NotificationManager";
import { userCan } from "functions/userCan";
import swal from "sweetalert";
import { BannerData } from "./BannerData";
import BannerTimeLine from "./TimeLineModal";

function BannerList({ allbanners, loading, setPage, limit, setLimit, refetch }) {
  const history = useHistory();
  const { formatMessage } = useIntl();
  const [BannerId, setBannerId] = useState();

  const [deleteBanner] = useMutation(DeleteBanner);
  const [banners, setBanners] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = banners;

  useEffect(() => {
    setBanners({
      collection: allbanners?.banners?.collection,
      metadata: allbanners?.banners?.metadata,
    });
  }, [allbanners]);
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);
  const handelDeleteBanner = (id) => {
    swal({
      title: formatMessage({ id: "are.u.sure.?" }),
      text: formatMessage({ id: "u.want.delete.banner" }),
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "delete" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        deleteBanner({
          variables: {
            id,
          },
        }).then(() => {
          refetch();
          NotificationManager.success(<FormattedMessage id="bannerdeletedsuccessfully" />);
        });
      }
    });
  };
  const getBannetAudits = (id) => {
    setBannerId(id);
    setOpenTimeLineModal(true);
  };

  const shareHandler = (bannerId) => {
    const url = `https://carwah.com?bannerId=${bannerId}`;
    navigator.clipboard.writeText(url).then(() => {
      swal({
        title: formatMessage({ id: "Link Copied" }),
        icon: "success",
      });
    });
  };

  const actions = ({ id, isDeepLink }) => (
    <div className="d-flex align-items-center" style={{ gap: "5px" }}>
      {/* Redirects to Car details */}

      {userCan("cars.update") && (
        <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
          <Link to={`banners/${id}/edit`}>
            <i className=" ti-pencil-alt m-1"></i>
          </Link>
        </Tooltip>
      )}
      <Tooltip title={formatMessage({ id: "common.delete" })} placement="top">
        <i
          style={{ cursor: "pointer" }}
          className=" ti-trash m-1"
          onClick={() => handelDeleteBanner(id)}
        ></i>
      </Tooltip>
      <Tooltip title={formatMessage({ id: "common.timeline" })} placement="top">
        <Link>
          <i className="fas fa-history" onClick={() => getBannetAudits(id)}></i>
        </Link>
      </Tooltip>
      {isDeepLink ? (
        <Tooltip title={formatMessage({ id: "copyBannerLink" })} placement="top">
          <div onClick={() => shareHandler(id)} style={{ cursor: "pointer" }}>
            <i className="ti-files" aria-hidden="true"></i>
          </div>
        </Tooltip>
      ) : null}
    </div>
  );
  return (
    <>
      <Typography component="div">
        <div>
          <RctCollapsibleCard fullBlock table>
            <CustomTable
              tableData={BannerData}
              loading={loading}
              tableRecords={collection}
              actions={actions}
              actionsArgs={["id", "isDeepLink"]}
            />
          </RctCollapsibleCard>
        </div>
        <div className="d-flex justify-content-around align-items-center">
          {metadata?.currentPage && (
            <>
              <TotalResults totalCount={metadata?.totalCount} />
              <Pagination
                count={Math.ceil(metadata?.totalCount / limit)}
                page={metadata?.currentPage}
                onChange={(e, value) => {
                  setPage(value);
                  history.replace({ hash: `page=${value}` });
                }}
              />
              <PerPage
                specialPagination={[10, 20, 40, 80, 100]}
                handlePerPageChange={(value) => setLimit(value)}
                perPage={limit}
                setPage={setPage}
              />
            </>
          )}
        </div>
      </Typography>
      <BannerTimeLine
        isOpen={opneTimeLineModal}
        setOpenTimeLineModal={setOpenTimeLineModal}
        BannerId={BannerId}
      />
    </>
  );
}

BannerList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  refetch: PropTypes.func,
  loading: PropTypes.bool,
  allbanners: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default BannerList;
