/* eslint-disable prettier/prettier */
/**
 * Bookings List
 */
import React, { useEffect, useState } from "react";
import { useIntl, FormattedMessage } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typo<PERSON>, Tooltip, Switch } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import TotalResults from "components/shared/TotalResults";
import { userCan } from "functions/userCan";
import { NotificationManager } from "react-notifications";
import { useMutation } from "@apollo/client";
import { ActivateAgency } from "gql/mutations/ActivateAgency.gql";
import swal from "sweetalert";
import { Data } from "./tableData";

function AgenciesList({ AgenciesRes, loading, setPage, limit, setLimit, refetch }) {
  const history = useHistory();
  const { formatMessage } = useIntl();
  const [activateAgency] = useMutation(ActivateAgency);
  const [agencies, setAgencies] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = agencies;
  useEffect(() => {
    setAgencies({
      collection: AgenciesRes?.agencies?.collection,
      metadata: AgenciesRes?.agencies?.metadata,
    });
  }, [AgenciesRes]);

  const actions = ({ id, isActive }) => (
    <div className="d-flex align-items-center">
      {userCan("agencies.update") && (
        <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
          <Link to={`agencies/${id}/edit`}>
            <i className=" ti-pencil-alt m-1"></i>
          </Link>
        </Tooltip>
      )}
      {userCan("agencies.activation") && (
        <Switch
          checked={isActive}
          color="primary"
          onChange={(e) => {
            if (e.target.checked) {
              swal({
                title: formatMessage({ id: "are.u.sure.?" }),
                text: formatMessage({ id: "you want to activate this agency" }),
                icon: "warning",
                buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
                dangerMode: true,
              }).then((result) => {
                if (result) {
                  activateAgency({
                    variables: {
                      agencyId: id,
                      isActive: true,
                    },
                  }).then(() => {
                    refetch();
                    NotificationManager.success(<FormattedMessage id="Activated Successfully" />);
                  });
                }
              });
            } else {
              swal({
                title: formatMessage({ id: "are.u.sure.?" }),
                text: formatMessage({ id: "you want to deactivate this agency" }),
                icon: "warning",
                buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
                dangerMode: true,
              }).then((willDelete) => {
                if (willDelete) {
                  activateAgency({
                    variables: {
                      agencyId: id,
                      isActive: false,
                    },
                  }).then(() => {
                    refetch();
                    NotificationManager.success(<FormattedMessage id="Deactivated successfully" />);
                  });
                }
              });
            }
          }}
          inputProps={{ "aria-label": "primary checkbox" }}
        />
      )}
    </div>
  );
  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={Data}
            loading={loading}
            tableRecords={collection}
            actions={actions}
            actionsArgs={["id", "isActive"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => setLimit(value)}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>
    </Typography>
  );
}

AgenciesList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  refetch: PropTypes.func,
  loading: PropTypes.bool,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  AgenciesRes: PropTypes.shape({
    agencies: PropTypes.shape({
      collection: PropTypes.array,
      metadata: PropTypes.shape({
        currentPage: PropTypes.number,
        totalCount: PropTypes.number,
      }),
    }),
  }),
};

export default AgenciesList;
