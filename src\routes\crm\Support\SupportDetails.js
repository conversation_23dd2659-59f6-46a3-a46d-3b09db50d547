import React, { useEffect,useState  } from "react";
import { useLocation, useParams, useHistory } from "react-router-dom";
import { Helmet } from "react-helmet";
import IntlMessages from "util/IntlMessages";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import { FeedbackDetails } from "gql/queries/FeedbackDetails.gql";
import { useQuery } from "@apollo/client";
import DotsLoader from "components/shared/DotsLoader";
import { FormattedMessage, useIntl } from "react-intl";
import { SupportDisplayData } from "./SupportDisplayData";
import TicketStatusModel from "./TicketStatusModel";
import { userCan } from "functions";

/**
 * @name FeatureDetails
 * @export
 * @return {JSX}
 */
export default function SupportDetailsComponent() {
  const location = useLocation();
  const { id } = useParams();
  const [isOpen,setIsopen]=useState()
  const [record,setRecord]=useState()
  const { formatMessage } = useIntl();
  const { data: feedbackDetails } = useQuery(FeedbackDetails, {
    variables: { id: +id },
  });
const {status}=feedbackDetails?.feedbackDetails || {}
  const handelManageTicket=(record)=>{

    setRecord(record)
    setIsopen(true)
      }
  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "sidebar.Support" })}</title>
        <meta name="description" content="Carwah Support Details" />
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="sidebar.Support"/>}
        enableBreadCrumb
        match={location}
        lastElement={id || <DotsLoader />}
        extraButtons={
          <>
            {userCan("feedbacks.manage") && (
               <button className="btn btn-primary" onClick={()=>handelManageTicket({id,status})}>
                   <FormattedMessage id="manage.ticket" />
              </button>
            )}
          </>
        }
      />
      <div className="row">
        {feedbackDetails && (
          <div className="w-50">
            <SupportDisplayData feedbackDetails={feedbackDetails} withimages={true} />
          </div>
        )}
      </div>
      {
isOpen && record &&
<TicketStatusModel setIsopen={setIsopen} isOpen={isOpen} record={record}/> 

      }
    </div>
  );
}
