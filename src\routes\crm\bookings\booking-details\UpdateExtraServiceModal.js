/* eslint-disable prettier/prettier */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/prop-types */
/* eslint-disable react/jsx-no-bind */

import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Button, Modal, ModalBody, ModalFooter } from "reactstrap";
import { CustomerUpdateRentalExtraServices } from "gql/mutations/UpdateRentalExtraService.gql";
import { useMutation } from "@apollo/client";
import { NotificationManager } from "react-notifications";
function UpdateExtraService({
  openextraService,
  setOpenExtraService,
  rentalDetails,
  carDetailsRes,
  refetch,
}) {
  function toggleHandler() {
    return setOpenExtraService(!openextraService);
  }
  const [customerUpdateRentalExtraServices] = useMutation(CustomerUpdateRentalExtraServices);
  const { locale, formatMessage } = useIntl();
  const [allyExtraServicesIds, setAllyExtraServicesIds] = useState([]);
  const [extraServices, setExtraServices] = useState({});
  const [branchExtraServicesIds, setBranchExtraServicesIds] = useState([]);

  const UpdateExtraService = () => {
    customerUpdateRentalExtraServices({
      variables: {
        allyExtraServices: allyExtraServicesIds,
        branchExtraServices: branchExtraServicesIds,
        rentalId: rentalDetails.id,
      },
    }).then(() => {
      NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
      refetch();
      setOpenExtraService(false);
    });
  };

  useEffect(() => {
    if (rentalDetails?.rentalExtraServices && carDetailsRes?.carProfile) {
      setExtraServices({
        allyExtraServicesForAlly:
          carDetailsRes?.carProfile?.branch?.allyCompany?.allyExtraServicesForAlly,
        branchExtraServices: carDetailsRes?.carProfile?.branch?.branchExtraServices,
      });
      const itemsArray = [];
      for (const item of carDetailsRes?.carProfile?.branch?.allyCompany?.allyExtraServicesForAlly) {
        if (item.isRequired) {
          itemsArray.push(item.id);
        } else if (
          rentalDetails?.rentalExtraServices
            ?.filter((s) => s.extraServiceType === "ally_company")
            ?.find((i) => +i.extraServiceId === +item.id)
        ) {
          itemsArray.push(item.id);
        }
      }
      setAllyExtraServicesIds([...allyExtraServicesIds, ...itemsArray]);
    }
    const itemsArray2 = [];
    for (const item of carDetailsRes?.carProfile?.branch?.branchExtraServices) {
      if (item.isRequired) {
        itemsArray2.push(item.id);
      } else if (
        rentalDetails?.rentalExtraServices
          ?.filter((s) => s.extraServiceType === "branch")
          ?.find((i) => +i.extraServiceId === +item.id)
      ) {
        itemsArray2.push(item.id);
      }
    }
    setBranchExtraServicesIds([...branchExtraServicesIds, ...itemsArray2]);
  }, []);
  return (
    <div>
      <Modal isOpen={openextraService} toggle={toggleHandler}>
        <ModalBody>
          <div className="mt-3 mb-3 row">
            {extraServices?.allyExtraServicesForAlly?.map((i) => (
              <div key={i.id} style={{ gap: "5px" }} className="d-flex col-3">
                <div className="col-1 d-block">
                  <input
                    id={`ally-extra-${i.id}`}
                    style={{ cursor: "pointer", height: "19px" }}
                    type="checkbox"
                    defaultChecked={
                      i.isRequired ||
                      rentalDetails?.rentalExtraServices
                        ?.filter((s) => s.extraServiceType === "ally_company")
                        ?.find((item) => +item.extraServiceId === +i.id) ||
                      allyExtraServicesIds.includes(i.id)
                    }
                    disabled={i.isRequired}
                    onChange={(e) => {
                      e.target.checked
                        ? setAllyExtraServicesIds([...allyExtraServicesIds, i.id])
                        : setAllyExtraServicesIds(
                            allyExtraServicesIds.filter((item) => +item !== +i.id),
                          );
                    }}
                  />
                </div>
                <label htmlFor={`ally-extra-${i.id}`} style={{ cursor: "pointer" }}>
                  {i.extraService[`${locale}Title`] && (
                    <h5 style={{ fontSize: "14px" }}>{i.extraService[`${locale}Title`]}</h5>
                  )}
                  {i.extraService?.subtitle && (
                    <h6 style={{ fontSize: "12px" }}>{i.extraService?.subtitle}</h6>
                  )}
                </label>
              </div>
            ))}
            {extraServices?.branchExtraServices?.map((i) => (
              <div key={i.id} style={{ gap: "5px" }} className="d-flex col-3">
                <div className="col-1 d-block">
                  <input
                    id={`branch-extra-${i.id}`}
                    style={{ cursor: "pointer", height: "19px" }}
                    type="checkbox"
                    defaultChecked={
                      i.isRequired ||
                      rentalDetails?.rentalExtraServices
                        ?.filter((s) => s.extraServiceType === "branch")
                        ?.find((item) => +item.extraServiceId === +i.id) ||
                      branchExtraServicesIds.includes(i.id)
                    }
                    disabled={i.isRequired}
                    onChange={(e) => {
                      e.target.checked
                        ? setBranchExtraServicesIds([...branchExtraServicesIds, i.id])
                        : setBranchExtraServicesIds(
                            branchExtraServicesIds.filter((item) => +item !== +i.id),
                          );
                    }}
                  />
                </div>
                <label htmlFor={`branch-extra-${i.id}`} style={{ cursor: "pointer" }}>
                  {i.allyExtraService?.extraService?.arTitle && (
                    <h5 style={{ fontSize: "14px" }}>
                      {i.allyExtraService?.extraService?.arTitle}
                    </h5>
                  )}
                  {i.subtitle && <h6 style={{ fontSize: "12px" }}>{i.subtitle}</h6>}
                </label>
              </div>
            ))}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            onClick={() => {
              UpdateExtraService();
            }}
          >
            <FormattedMessage id="Edit" />
          </Button>
          <Button
            color="danger"
            onClick={() => {
              setOpenExtraService(false);
            }}
          >
            <FormattedMessage id="cancel" />
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default UpdateExtraService;
