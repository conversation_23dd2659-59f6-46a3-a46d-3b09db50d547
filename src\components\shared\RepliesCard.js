import React, { useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import moment from "moment";

const RepliesCard = ({ replies }) => {
  const { locale } = useIntl();
  const [selectedImage, setSelectedImage] = useState(null);

  if (!replies?.length) return null;

  return (
    <div className="mt-4">
      <div className="replies-container" style={{ maxHeight: "400px", overflowY: "auto" }}>
        {replies.map((reply) => (
          <div 
            key={reply.id || reply.createdAt}
            className="reply-card mb-3 p-3"
            style={{
              backgroundColor: "#f8f9fa",
              borderRadius: "8px",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              borderLeft: "4px solid #007bff"
            }}
          >
            <div className="d-flex justify-content-between align-items-center mb-2">
              <div className="d-flex align-items-center">
                <i className="zmdi zmdi-account-circle mr-2" style={{ fontSize: "20px" }} />
                <span className="font-weight-bold">{reply.repliedByName}</span>
              </div>
              <small className="text-muted">
                {moment(reply?.createdAt).locale(locale).format("DD/MM/YYYY h:mm:ss a")}
              </small>
            </div>
            <div className="reply-content">
              {reply.replyText}
              {reply.replyAttachment && (
                <div className="mt-2">
                  <img 
                    src={reply.replyAttachment} 
                    alt="Reply attachment" 
                    className="img-fluid rounded"
                    style={{ 
                      maxWidth: "300px", 
                      maxHeight: "200px", 
                      objectFit: "contain",
                      cursor: "pointer"
                    }}
                    onClick={() => setSelectedImage(reply.replyAttachment)}
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Full Screen Modal */}
      {selectedImage && (
        <div 
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            padding: "20px"
          }}
          onClick={() => setSelectedImage(null)}
        >
          <img 
            src={selectedImage} 
            alt="Full screen attachment" 
            style={{ 
              maxWidth: "90%", 
              maxHeight: "90vh", 
              objectFit: "contain" 
            }}
          />
          <button
            onClick={() => setSelectedImage(null)}
            style={{
              position: "absolute",
              top: "20px",
              right: "20px",
              background: "none",
              border: "none",
              color: "white",
              fontSize: "30px",
              cursor: "pointer"
            }}
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};

RepliesCard.propTypes = {
  replies: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      createdAt: PropTypes.string,
      repliedByName: PropTypes.string,
      body: PropTypes.string,
      replyAttachment: PropTypes.string
    })
  )
};

export default RepliesCard; 