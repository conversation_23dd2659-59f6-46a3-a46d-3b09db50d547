/* eslint-disable prettier/prettier */
/* eslint-disable radix */
/* eslint-disable eqeqeq */
/* eslint-disable no-undefined */
import React, { useEffect } from "react";
import moment from "moment";
import { useParams } from "react-router-dom";
import { NotificationManager } from "react-notifications";
import { FormattedMessage } from "react-intl";

function useEffectsAgency({
  refetchBooking,
  AreasRes,
  isAgency,
  getCarDetails,
  getAreasQuery,
  allyCompanies,
  rentalDetails,
  locale,
  setAllCompanies,
  area,
  changed,
  setPickUpCity,
  CarsRes,
  setAvailableCarsCollection,
  setAvaiableCarsDD,
  carName,
  carDetailsRes,
  bookingType,
  pickUpDate,
  dropOffDate,
  setMonths,
  setDropOffDate,
  setDropOffCity,
  setSelectedDropoffBranch,
  monthTime,
  ready,
  editDatedReady,
  setDeliveryPrice,
  setHandOverPrice,
  setInsuranceId,
  setIsTwoWays,
  setPlan,
  setCustomerId,
  setCopounId,
  setCouponCode,
  copounref,
  setunLimited,
  dropOffCity,
  pickUpCity,
  isPickSameReturn,
  selectedDropoffBranch,
  suggestedPricePerDayRef,
  setBookingType,
  setEditDatedReady,
  setPickUpDate,
  setPaymentMethod,
  setReady,
  setDistanceCarUser,
  setSelectedCar,
  setCarPlans,
  setDeliverLng,
  setDeliverLat,
  selectedCompany,
  getBranches,
  setSelectedBranch,
  selectedBranch,
  allBranches,
  setSelectedCompany,
  allCompanies,
  branchExtraServicesIds,
  setBranchExtraServicesIds,
  allyExtraServicesIds,
  setAllyExtraServicesIds,
  setExtraServices,
  branches,
  setFusranChecked,
  selectedCar,
  requestCarsvariables,
  getCars,
  mapChange,
  deliverLat,
  handoverprice,
  distanceCarUser,
  deliverLng,
  setIsPickSameReturn,
  setIsDelivery,
  isHandover,
  isRentalInstallment,
  withInstallment,
  BookingPriceRes,
}) {
  const { bookingId } = useParams();

  useEffect(() => {
    const AreaData = async () => {
      if (bookingId && !AreasRes?.length) {
        await refetchBooking();
        await getCarDetails();
        await getAreasQuery();
      } else {
        await getAreasQuery();
      }
    };
    AreaData();
  }, []);

  useEffect(() => {
    if (allyCompanies?.availableAllyCompanies?.collection?.length) {
      const formattedList =
        allyCompanies.availableAllyCompanies.collection
          .filter((item) => item.isB2c || +item.id == +rentalDetails?.rentalDetails?.allyCompanyId)
          .map((item) => ({
            value: item?.id,
            label: item?.[`${locale}Name`],
            isExtendFixedPrice: item.isExtendFixedPrice,
          })) || [];
      setAllCompanies(formattedList);
    } else {
      setAllCompanies([]);
    }
  }, [allyCompanies, rentalDetails?.rentalDetails]);
  useEffect(() => {
    if (area && changed) {
      setPickUpCity(area.area);
    }
  }, [area]);

  useEffect(() => {
    if (CarsRes?.listCars?.length) {
      const availableCars = [...CarsRes?.listCars];
      setAvailableCarsCollection(availableCars);
      const selectedCar = availableCars?.find(
        (car) => +car.id === +rentalDetails?.rentalDetails?.carId,
      );
      setAvaiableCarsDD(
        availableCars?.map((car) => ({
          label: carName(car),
          value: car.id,
          isUnlimited: car.isUnlimited,
          unlimitedFeePerDay: car.unlimitedFeePerDay,
        })),
      );
      if (!changed) {
        setSelectedCar(selectedCar);
      }
    } else {
      setAvailableCarsCollection([]);
      setAvaiableCarsDD([]);
    }
  }, [CarsRes, carDetailsRes]);

  useEffect(() => {
    if (bookingType === "daily" && bookingId) {
      setDropOffDate(
        moment(
          `${rentalDetails?.rentalDetails.dropOffDate}T${rentalDetails?.rentalDetails.dropOffTime}Z`,
        )
          // .locale("en")
          .subtract(Math.abs(new Date().getTimezoneOffset() / 60), "hours"),
      );
    } else if (bookingType === "monthly" && bookingId) {
      if (rentalDetails?.rentalDetails.dropOffDate) {
        setDropOffDate(
          moment(rentalDetails?.rentalDetails.dropOffDate)
            // .locale("en")
            .hours(rentalDetails?.rentalDetails?.dropOffTime?.split(":")[0])
            .minutes(rentalDetails?.rentalDetails?.dropOffTime?.split(":")[1]),
        );
      }
    }
  }, [bookingType]);

  useEffect(() => {
    if (monthTime) {
      setDropOffDate(
        moment(dropOffDate)
          .hours(monthTime?.split(":")[0])
          .locale("en")
          .minutes(monthTime?.split(":")[1]),
      );
    }
  }, [monthTime]);

  useEffect(() => {
    // EDIT MODE
    if (rentalDetails?.rentalDetails && !ready && !editDatedReady && !changed) {
      setDeliveryPrice(rentalDetails?.rentalDetails.deliveryPrice);
      if (rentalDetails?.rentalDetails?.loyaltyType) {
        setFusranChecked(true);
      }
      if (rentalDetails?.rentalDetails.deliverType === "two_ways") {
        setIsTwoWays(true);
      }
      const {
        pickUpDate,
        pickUpTime,
        dropOffTime,
        dropOffDate,
        deliverLat,
        userId,
        // note,
        couponId,
        couponCode,
        insuranceId,
      } = rentalDetails?.rentalDetails;
      setInsuranceId(insuranceId);

      // SETTING CUSTOMER ID to fire customer details query
      setPlan(rentalDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan);
      setCustomerId(userId);
      setCopounId(couponId);
      setCouponCode(couponCode);
      copounref.current = couponCode;
      // Noteref.current = note;
      setunLimited(rentalDetails?.rentalDetails.isUnlimited);
      // TIMES

      setPickUpDate(
        moment(`${pickUpDate}T${pickUpTime}Z`)
          // .locale("en")
          // .locale("en")
          .subtract(Math.abs(new Date().getTimezoneOffset() / 60), "hours"),
      );
      setDropOffDate(
        moment(`${dropOffDate}T${dropOffTime}Z`)
          // .locale("en")
          .subtract(Math.abs(new Date().getTimezoneOffset() / 60), "hours"),
      );
      setEditDatedReady(true);
      if (
        rentalDetails?.rentalDetails?.numberOfDays % 30 === 0 &&
        !rentalDetails.rentalDetails.isRentToOwn
      ) {
        setMonths(rentalDetails?.rentalDetails?.numberOfDays / 30);
        setBookingType("monthly");
      } else if (rentalDetails.rentalDetails.isRentToOwn) {
        setBookingType("rent-to-own");
      }

      if (rentalDetails?.rentalDetails?.suggestedPrice) {
        suggestedPricePerDayRef.current = rentalDetails?.rentalDetails?.suggestedPrice;
      }
      setSelectedDropoffBranch(rentalDetails?.rentalDetails?.dropOffBranchId);
    }

    // to set old used cities locations
    if (
      (rentalDetails?.rentalDetails?.deliverLat || rentalDetails?.rentalDetails?.deliverLng) &&
      !ready
    ) {
      setDeliverLat(rentalDetails?.rentalDetails?.deliverLat);
      setDeliverLng(rentalDetails?.rentalDetails?.deliverLng);
      setIsDelivery(true);
    }
    if (bookingId && carDetailsRes?.carProfile && !ready) {
      setCarPlans(carDetailsRes?.carProfile?.ownCarDetail?.ownCarPlans);
      setSelectedCar(carDetailsRes.carProfile);
      setDistanceCarUser(carDetailsRes?.carProfile?.distanceBetweenCarUser);
      if (rentalDetails?.rentalDetails.subStatus == "ally_declined") {
        setInsuranceId(null);
      } else if (changed) {
        setInsuranceId(carDetailsRes?.carProfile?.carInsurances[0]?.id);
      }

      setReady(true);
    }
    if (rentalDetails?.rentalDetails?.paymentMethod) {
      setPaymentMethod(rentalDetails?.rentalDetails?.paymentMethod);
    }
  }, [rentalDetails?.rentalDetails, AreasRes, CarsRes]);

  useEffect(() => {
    if (rentalDetails?.rentalDetails?.allyCompanyId) {
      getBranches({
        variables: {
          allyCompanyIds: selectedCompany?.value
            ? [selectedCompany?.value]
            : [rentalDetails?.rentalDetails?.allyCompanyId],
          areaIds: pickUpCity?.id ? [pickUpCity?.id] : [rentalDetails.rentalDetails.pickUpCityId],
          canDelivery: bookingType === "delivery" ? true : undefined,
          isActive: true,
          isRentToOwn: bookingType == "rent-to-own" ? true : undefined,
          pickStartDate:
            rentalDetails?.rentalDetails && bookingId
              ? moment(pickUpDate?._id || pickUpDate)
                  .locale("en")
                  .format("DD/MM/YYYY")
              : moment(pickUpDate?._id || pickUpDate)
                  .locale("en")
                  .format("DD/MM/YYYY"),
        },
      });
    }
  }, [rentalDetails?.rentalDetails]);

  useEffect(() => {
    if (!changed && allCompanies && !selectedCompany) {
      setSelectedCompany(
        allCompanies.find((i) => +i.value === +rentalDetails?.rentalDetails?.allyCompanyId),
      );
    }
    if (
      !changed &&
      allBranches &&
      !selectedBranch &&
      selectedCompany?.value == rentalDetails?.rentalDetails?.allyCompanyId
    ) {
      setSelectedBranch(
        allBranches.find((i) => +i.value === +rentalDetails?.rentalDetails?.branchId),
      );
    }
  }, [allCompanies, allBranches, CarsRes]);

  useEffect(() => {
    // to set old used cities locations
    if (!changed) {
      if (AreasRes && rentalDetails?.rentalDetails) {
        setIsPickSameReturn(
          +rentalDetails?.rentalDetails?.branchId ===
            +rentalDetails?.rentalDetails?.dropOffBranchId,
        );
        setPickUpCity(
          AreasRes?.areas.find((area) => +area?.id === +rentalDetails?.rentalDetails?.pickUpCityId),
        );
        setDropOffCity(
          AreasRes?.areas.find(
            (area) => +area?.id === +rentalDetails?.rentalDetails?.dropOffCityId,
          ),
        );
      }
    }
  }, [AreasRes]);

  useEffect(() => {
    getCars({
      variables: {
        ...requestCarsvariables,
        canDelivery: bookingType === "delivery" ? true : undefined,
        isRentToOwn: bookingType == "rent-to-own" ? true : undefined,
        carId: selectedCar?.id,
        agencyId: isAgency ? isAgency : undefined,
      },
    });
    if (selectedCar?.id) {
      setExtraServices({
        branchExtraServices: selectedCar.branch.branchExtraServices,
        allyExtraServicesForAlly: selectedCar.branch.allyCompany.allyExtraServicesForAlly,
      });
      setSelectedBranch(selectedCar.branch);
      setSelectedCompany(selectedCar.branch.allyCompany);
    }

    // adds required services to ids
  }, [selectedCar?.id, pickUpCity]);
  useEffect(() => {
    if (pickUpCity?.id && selectedCompany?.value) {
      getBranches({
        variables: {
          allyCompanyIds: selectedCompany?.value
            ? [selectedCompany?.value]
            : [rentalDetails?.rentalDetails?.allyCompanyId],
          areaIds: pickUpCity?.id ? [pickUpCity?.id] : undefined,
          isActive: true,
          limit: 500,
          canDelivery: bookingType === "delivery" ? true : undefined,
          isRentToOwn: bookingType == "rent-to-own" ? true : undefined,
          pickStartDate:
            rentalDetails?.rentalDetails && bookingId
              ? moment(pickUpDate?._id || pickUpDate)
                  .locale("en")
                  .format("DD/MM/YYYY")
              : moment(pickUpDate?._id || pickUpDate)
                  .locale("en")
                  .format("DD/MM/YYYY"),
        },
      });
    }
  }, [pickUpCity, selectedCompany]);

  useEffect(() => {
    if (selectedCar && selectedBranch && distanceCarUser) {
      const deliveryprice = selectedBranch?.branchDeliveryPrices?.filter(
        (branch) => +branch.from <= +distanceCarUser && branch.to >= +distanceCarUser,
      );
      if (rentalDetails?.rentalDetails.subStatus == "ally_declined") {
        setInsuranceId(null);
      } else if (changed) {
        setInsuranceId(selectedCar.carInsurances[0]?.id);
      }

      if (deliveryprice.length && changed) {
        setDeliveryPrice(deliveryprice[0].deliveryPrice);
        if (
          deliveryprice[0].handoverPrice != undefined &&
          selectedBranch?.canHandover &&
          rentalDetails?.rentalDetails?.branchId !==
            rentalDetails?.rentalDetails?.dropOffBranchId &&
          (!bookingId || !handoverprice)
        ) {
          if (isHandover) setHandOverPrice(deliveryprice[0].handoverPrice);
          // setHandOverChecked(true);
        }
        if (
          deliveryprice[0].handoverPrice != undefined &&
          selectedBranch?.canHandover &&
          (!bookingId || !handoverprice)
        ) {
          if (isHandover) setHandOverPrice(deliveryprice[0].handoverPrice);
        }
      }

      if (
        selectedBranch?.canHandover &&
        rentalDetails?.rentalDetails?.branchId !== rentalDetails?.rentalDetails?.dropOffBranchId
      ) {
        // setHandOverChecked(true);
      }
    }
  }, [selectedCar, selectedBranch, isPickSameReturn, changed]);

  useEffect(() => {
    if (BookingPriceRes?.aboutRentPrice?.handoverPrice && !handoverprice) {
      setHandOverPrice(BookingPriceRes?.aboutRentPrice?.handoverPrice);
    } else if (!changed) {
      setHandOverPrice(rentalDetails?.rentalDetails?.handoverPrice);
    }
  }, [
    BookingPriceRes?.aboutRentPrice?.handoverPrice,
    handoverprice,
    rentalDetails?.rentalDetails?.handoverPrice,
  ]);

  useEffect(() => {
    if (deliverLat) {
      const area = AreasRes?.areas.filter(
        (area) =>
          Number.parseFloat(area?.centerLat).toFixed(2) <=
            Number.parseFloat(deliverLat).toFixed(2) &&
          Number.parseInt(area?.centerLat) == Number.parseInt(deliverLat),
      );
      // if (deliverLat && deliverLng && area?.length) {
      //   setDeliveryPrice(null);
      //   // setPickUpCity(area[0] || {});
      // }
    }
  }, [deliverLat, deliverLng, mapChange]);

  useEffect(() => {
    if (rentalDetails?.rentalDetails.subStatus == "ally_declined") {
      setInsuranceId(null);
    }
  }, [rentalDetails?.rentalDetails]);
}

export default useEffectsAgency;
