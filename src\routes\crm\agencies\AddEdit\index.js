// Component is used for Carwah admin to add/edit agency and shared also in add/edit profile of Agency
import CustomTextField from "components/Input/CustomTextField";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import React, { memo, useEffect, useRef, useState } from "react";
import { Helmet } from "react-helmet";
import { FormattedMessage, useIntl } from "react-intl";
import IntlMessages from "util/IntlMessages";
import { CreateAgency, UpdateAgency } from "gql/mutations/Agencies.gql";
import { useMutation, useQuery } from "@apollo/client";
import { Controller, useForm } from "react-hook-form";
import { emailPattern, strongPasswordRegex } from "constants/regex";
import IntlTelInput from "react-intl-tel-input";
import AllyName from "components/DropDowns/AllyName";
import { FileUploader } from "components/ImageUploader";
import { ImageUpload } from "gql/mutations/UploadImage.gql";
import { <PERSON><PERSON><PERSON><PERSON>on, TextField, InputAdornment, Button } from "@material-ui/core";
import { Visibility, VisibilityOff } from "@material-ui/icons";
import { NotificationManager } from "react-notifications";
import { useHistory, useParams } from "react-router-dom";
import { Agency } from "gql/queries/Agencies.gql";
import { userCan } from "functions";
import { useSelector } from "react-redux";

function AddEdit() {
  const history = useHistory();
  const telephoneRef = useRef();
  const { agencyId: paramAgencyId } = useParams();
  const isEditMode = history.location.pathname.includes("edit");
  const isAddMode = history.location.pathname.includes("add");
  const isProfile = history.location.pathname.includes("profile");
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const isAgency = user_data?.agency_id;
  const agencyId = paramAgencyId || user_data?.agency_id;
  const { formatMessage } = useIntl();
  const [loader, setLoader] = useState(false);
  const [isMobileValid, setIsMobileValid] = useState(true);
  const [allAlliesSelected, setAllAlliesSelected] = useState(false);
  const [isSuperAdminMobileValid, setIsSuperAdminMobileValid] = useState(true);
  const [togglePassword, setTogglePassword] = useState(false);
  const { user } = useSelector((state) => state.authUser) || {};
  const [disabled, setDisabled] = useState(true);
  const { user_type } = user || {};
  const {
    control,
    watch,
    handleSubmit,
    formState: { errors, isDirty, dirtyFields },
    setValue,
    reset,
  } = useForm({ mode: "all", reValidateMode: "onChange" });
  const [createAgency, { loading: CreateLoader }] = useMutation(CreateAgency, {
    errorPolicy: "all",
  });
  const [updateAgency, { loading: UpdateLoader }] = useMutation(UpdateAgency, {
    errorPolicy: "all",
  });
  const [imageUpload] = useMutation(ImageUpload);
  const {
    data: agency,
    refetch,
    loading: AgencyLoader,
  } = useQuery(Agency, {
    skip: !agencyId,
    variables: {
      id: +agencyId,
    },
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (user_type === "agency_customer_service") {
      history.push("/cw/dashboard/customers");
    }
    if (agency) {
      reset({
        ...agency.agency,
        allyCompanyIds: agency.agency.allyCompanyIds.map((i) => +i),
        agencyId,
        agencyKey: agency.agency.agencyKey,
      });
    }
  }, [agency]);

  function generateCode(sentence) {
    if (!sentence) return undefined;
    const initials = sentence
      .split(" ")
      .map((word) => word[0])
      .join("");

    const randomLength = 6 - initials.length;

    const randomNumbers = Math.floor(Math.random() * Math.pow(10, randomLength))
      .toString()
      .padStart(randomLength, "0"); // Ensure the numbers have leading zeros if needed

    setValue("agencyKey", initials + randomNumbers);
    // reset({ ...watch(), agencyKey: initials + randomNumbers });
  }

  const uploadImage = (file, field) => {
    setLoader(true);
    imageUpload({
      variables: {
        image: file,
        topic: "Image",
      },
    }).then((res) => {
      setLoader(false);
      field.onChange(res.data.imageUpload.imageUpload.imageUrl);
    });
  };
  const onSubmit = (data) => {
    const variables = {
      ...data,
      phoneNumber: data.phoneNumber,
      allyCompanyIds:
        watch("allyCompanyIds")?.length && !allAlliesSelected ? watch("allyCompanyIds") : [],
    };
    if (agencyId) {
      updateAgency(
        { variables: { ...variables } }
      )
        .then((res) => {
          if (res?.errors) {
            res.errors.map(({ message }) => {
              NotificationManager.error(message);
            });
            return;
          }
          NotificationManager.success(<FormattedMessage id="Edited Successfully" />);
          history.push(
            paramAgencyId ? `/cw/dashboard/agencies/${paramAgencyId}` : "/cw/dashboard/profile",
          );
          refetch();
        })
        .catch((error) => NotificationManager.error(error));
      return;
    }

    createAgency(
      { variables: { ...variables } }

    )
      .then((res) => {
        if (res?.errors) {
          res.errors.map(({ message }) => {
            NotificationManager.error(message);
          });
          return;
        }
        NotificationManager.success(<FormattedMessage id={"AddSuccessfully"} />);
        history.push("/cw/dashboard/agencies");
      })
      .catch((error) => NotificationManager.error(error));
  };

  return (
    <div className="clients-wrapper">
      <Helmet>
        <title>
          {formatMessage({
            id: agencyId && !isProfile ? "Edit Agency" : isProfile ? "Settings" : "Add Agency",
          })}
        </title>
      </Helmet>

      <PageTitleBar
        title={
          <IntlMessages
            id={isEditMode && !isProfile ? "Edit Agency" : isProfile ? "Settings" : "Add Agency"}
          />
        }
        match={location}
        lastElement={
          isEditMode ? (
            agencyId && !isProfile ? (
              agencyId
            ) : (
              <IntlMessages id={"Profile"} />
            )
          ) : (
            <IntlMessages id={isProfile ? "Profile" : "Add Agency"} />
          )
        }
        enableBreadCrumb
        extraButtons={
          <>
            {userCan("agencies.update") && isProfile && agency?.agency?.isActive && !isEditMode && (
              <Button
                variant="contained"
                color="primary"
                className="btn btn-success"
                onClick={() => history.push("profile/edit")}
              >
                <IntlMessages id="Edit" />
              </Button>
            )}
          </>
        }
      />
      {!isEditMode || (isEditMode && !AgencyLoader) ? (
        <form onSubmit={handleSubmit(onSubmit)} autoComplete="off" key={agency}>
          <div className="row mt-5 pt-3">
            <div className="col-md-12 d-block mb-2 ">
              <h4 style={{ fontWeight: "bold" }}>
                <FormattedMessage id="Agency Details" />
              </h4>
            </div>
            <div className="col-md-6 my-2">
              <Controller
                defaultValue={agency?.agency?.nameAr}
                name={"nameAr"}
                control={control}
                rules={{
                  required: true,
                  minLength: 1,
                  maxLength: 255,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      defaultValue={field.value}
                      fullWidth
                      onchange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Agency Name (Ar)" })}
                      label={formatMessage({ id: "Agency Name (Ar)" })}
                      error={errors?.nameAr}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.nameAr?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.nameAr?.type === "minLength" ||
                      (errors?.nameAr?.type === "maxLength" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="Input is not valid" />
                        </helperText>
                      ))}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2">
              <Controller
                defaultValue={agency?.agency?.nameEn}
                name={"nameEn"}
                control={control}
                rules={{
                  required: true,
                  minLength: 1,
                  maxLength: 255,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      defaultValue={field.value}
                      fullWidth
                      value={watch("nameEn")}
                      onchange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      onblur={(e) => {
                        generateCode(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Agency Name (En)" })}
                      label={formatMessage({ id: "Agency Name (En)" })}
                      error={errors?.nameEn}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.nameEn?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.nameEn?.type === "minLength" ||
                      (errors?.nameEn?.type === "maxLength" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="Input is not valid" />
                        </helperText>
                      ))}
                  </>
                )}
              />
            </div>
            {isAddMode || (isProfile && !isEditMode) ? (
              <div className="col-md-6 my-2">
                <Controller
                  key={`${watch("enName")} ${!watch("agencyKey") ? watch("agencyKey") : null}`}
                  defaultValue={watch("agencyKey") || agency?.agency?.agencyKey}
                  name={"agencyKey"}
                  control={control}
                  rules={{
                    required: true,
                    minLength: 1,
                    maxLength: 6,
                  }}
                  render={({ field, fieldState }) => (
                    <>
                      <CustomTextField
                        defaultValue={field.value}
                        fullWidth
                        onchange={(e) => {
                          field.onChange(e.target.value);
                        }}
                        type="text"
                        placeholder={formatMessage({ id: "Agency Key" })}
                        label={formatMessage({ id: "Agency Key" })}
                        error={errors?.agencyKey}
                        disabled={!isEditMode && !isAddMode}
                      />
                      {errors?.agencyKey?.type === "required" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="thisfieldisrequired" />
                        </helperText>
                      )}
                      {errors?.agencyKey?.type === "minLength" ||
                        (errors?.agencyKey?.type === "maxLength" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="Input is not valid" />
                          </helperText>
                        ))}
                    </>
                  )}
                />
              </div>
            ) : null}

            <div className="col-md-6 my-2">
              <Controller
                defaultValue={watch("email") || agency?.agency?.email}
                name={"email"}
                control={control}
                rules={{
                  required: true,
                  pattern: emailPattern,
                }}
                render={({ field, fieldState, formState }) => (
                  <>
                    <CustomTextField
                      defaultValue={field.value}
                      fullWidth
                      onblur={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Email" })}
                      onchange={(e) => {
                        if (
                          e?.target?.value &&
                          !emailPattern.test(e?.target?.value) &&
                          !errors?.email
                        ) {
                          field.onChange(e.target.value);
                        } else {
                          if (emailPattern.test(e?.target?.value) && errors?.email) {
                            field.onChange(e.target.value);
                          }
                        }
                      }}
                      label={formatMessage({ id: "Email" })}
                      error={errors?.email}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.email?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.email?.type === "pattern" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="validation.emailFormatPleaseFormat" />
                      </helperText>
                    )}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2" dir="ltr">
              <Controller
                name="phoneNumber"
                control={control}
                defaultValue={agency?.agency?.phoneNumber?.replaceAll(" ", "") || ""} // Ensure the default value is set
                rules={{
                  required: true,
                }}
                render={({ field, fieldState: { error }, formState }) => (
                  <>
                    <IntlTelInput
                      ref={telephoneRef}
                      fieldId="input-tel"
                      // separateDialCode
                      telInputProps={{ pattern: "[0-9]*" }}
                      preferredCountries={[
                        (field?.value && field?.value?.startsWith("966")) ||
                        (field?.value && field?.value?.startsWith("+966")) ||
                        !isEditMode
                          ? "sa"
                          : "eg",
                      ]}
                      containerClassName="intl-tel-input"
                      // placeholder="512345678*"
                      defaultValue={field.value} // Keep the value in sync
                      onPhoneNumberChange={(isValid, num, obj, fullNum) => {
                        setIsMobileValid(isValid);

                        if (
                          agency?.agency?.phoneNumber &&
                          agency?.agency?.phoneNumber?.replaceAll(" ", "") !=
                            fullNum?.replaceAll(" ", "") &&
                          isValid
                        ) {
                          field.onChange(fullNum?.replaceAll(" ", ""));
                        }
                      }}
                      onPhoneNumberBlur={(isValid, num, obj, fullNum) => {
                        isDirty && setIsMobileValid(isValid);
                        if (isValid) {
                          field.onChange(fullNum?.replaceAll(" ", "")); // Update the form value
                          // setValue("code", obj.dialCode);
                        } else {
                          field.onChange("");
                          // setValue("code", "");
                        }
                        // setValue("phoneNumber", num, {
                        //   shouldValidate: true, // Validate the field after change
                        //   shouldDirty: true, // Mark the field as dirty
                        // });
                      }}
                      // onPhoneNumberChange={}
                      disabled={!isEditMode && !isAddMode}
                      nationalMode={false} // Disable national mode
                      autoFormat={false} // Prevent automatic formatting
                      formatOnDisplay={false} // Prevent display formatting
                      format={false}
                      formatOnInit={false}
                      separateDialCode={true}
                    />
                    {error?.type === "required" && isMobileValid && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {!isMobileValid && (
                      <helperText className="text-danger">
                        <FormattedMessage id="please enter correct phone format" />
                      </helperText>
                    )}
                  </>
                )}
              />
            </div>

            <div className="col-md-6 my-2">
              <Controller
                defaultValue={agency?.agency?.vatRegistrationId}
                name={"vatRegistrationId"}
                control={control}
                rules={{
                  required: true,
                  minLength: 1,
                  maxLength: 15,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      defaultValue={field.value}
                      fullWidth
                      onchange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Vat Registration ID" })}
                      label={formatMessage({ id: "Vat Registration ID" })}
                      error={errors?.vatRegistrationId}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.vatRegistrationId?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.vatRegistrationId?.type === "minLength" ||
                      (errors?.vatRegistrationId?.type === "maxLength" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="Input is not valid" />
                        </helperText>
                      ))}
                  </>
                )}
              />
            </div>
            {!isAgency ? (
              <div className="col-md-6 my-2">
                <Controller
                  name={"allyCompanyIds"}
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field, fieldState }) => (
                    <>
                      <AllyName
                        valueAttribute="id"
                        selectedAlly={watch("allyCompanyIds")}
                        multiple={true}
                        onAllyChange={() => console.log("")}
                        setSelectedAlly={(ally) => {
                          field.onChange(ally?.map((item) => +item.value));
                        }}
                        isActive
                        setAllAlliesSelected={setAllAlliesSelected}
                      />
                      {errors?.allyCompanyIds?.type === "required" && isDirty && (
                        <helperText className="text-danger">
                          <FormattedMessage id="thisfieldisrequired" />
                        </helperText>
                      )}
                    </>
                  )}
                />
              </div>
            ) : null}
            {isProfile && !isEditMode && !agency?.agency?.logo ? null : (
              <div className="col-md-6 my-2">
                <Controller
                  name={"logo"}
                  value={watch("logo")}
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field, fieldState }) => (
                    <>
                      <FileUploader
                        loader={loader}
                        titleId={formatMessage({ id: "Agency Logo" })}
                        image={watch("logo")}
                        setImage={(file) => {
                          uploadImage(file, field);
                        }}
                        disabled={!isEditMode && !isAddMode}
                        imgremove={Boolean(watch("logo"))}
                        remove={() => setValue("logo", null, { shouldDirty: true })}
                      />
                    </>
                  )}
                />
              </div>
            )}
            {!agencyId && (
              <div className="row">
                <div className="col-md-12 d-block mb-2 ">
                  <h4 style={{ fontWeight: "bold" }}>
                    <FormattedMessage id="Super Admin" />
                  </h4>
                </div>
                <div className="col-md-6 my-2">
                  <Controller
                    name={"superAdminFirstName"}
                    control={control}
                    rules={{
                      required: true,
                      minLength: 1,
                      maxLength: 50,
                    }}
                    render={({ field, fieldState }) => (
                      <>
                        <CustomTextField
                          fullWidth
                          onchange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          type="text"
                          placeholder={formatMessage({ id: "First Name" })}
                          label={formatMessage({ id: "First Name" })}
                          error={errors?.superAdminFirstName}
                        />
                        {errors?.superAdminFirstName?.type === "required" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="thisfieldisrequired" />
                          </helperText>
                        )}
                        {errors?.superAdminFirstName?.type === "minLength" ||
                          (errors?.superAdminFirstName?.type === "maxLength" && (
                            <helperText className="text-danger">
                              <FormattedMessage id="Input is not valid" />
                            </helperText>
                          ))}
                      </>
                    )}
                  />
                </div>
                <div className="col-md-6 my-2">
                  <Controller
                    name={"superAdminLastName"}
                    control={control}
                    rules={{
                      required: true,
                      minLength: 1,
                      maxLength: 50,
                    }}
                    render={({ field, fieldState }) => (
                      <>
                        <CustomTextField
                          fullWidth
                          onchange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          type="text"
                          placeholder={formatMessage({ id: "Last Name" })}
                          label={formatMessage({ id: "Last Name" })}
                          error={errors?.superAdminLastName}
                        />
                        {errors?.superAdminLastName?.type === "required" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="thisfieldisrequired" />
                          </helperText>
                        )}
                        {errors?.superAdminLastName?.type === "minLength" ||
                          (errors?.superAdminLastName?.type === "maxLength" && (
                            <helperText className="text-danger">
                              <FormattedMessage id="Input is not valid" />
                            </helperText>
                          ))}
                      </>
                    )}
                  />
                </div>
                <div className="col-md-6 my-2">
                  <Controller
                    name={"superAdminEmail"}
                    control={control}
                    rules={{
                      required: true,
                      pattern: emailPattern,
                    }}
                    render={({ field, fieldState: { error } }) => (
                      <>
                        <CustomTextField
                          defaultValue={watch("superAdminEmail")}
                          fullWidth
                          onblur={(e) => {
                            field.onChange(e.target.value);
                          }}
                          type="text"
                          placeholder={formatMessage({ id: "Email" })}
                          label={formatMessage({ id: "Email" })}
                          error={errors?.superAdminEmail}
                        />
                        {errors?.superAdminEmail?.type === "required" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="thisfieldisrequired" />
                          </helperText>
                        )}
                        {errors?.superAdminEmail?.type === "pattern" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="validation.emailFormatPleaseFormat" />
                          </helperText>
                        )}
                      </>
                    )}
                  />
                </div>
                <div className="col-md-6 my-2" dir="ltr">
                  <Controller
                    name={"superAdminPhoneNumber"}
                    control={control}
                    rules={{
                      required: true,
                    }}
                    render={({ field, fieldState, error }) => (
                      <>
                        <IntlTelInput
                          fieldId="input-tel"
                          separateDialCode
                          telInputProps={{ pattern: "[0-9]*" }}
                          preferredCountries={["sa"]}
                          containerClassName="intl-tel-input"
                          // placeholder="512345678*"
                          defaultValue={watch("superAdminPhoneNumber")}
                          onPhoneNumberBlur={(isValid, num, obj, fullNum) => {
                            isDirty && setIsSuperAdminMobileValid(isValid);
                            if (isValid) {
                              field.onChange(fullNum?.replaceAll(" ", "")); // Update the form value
                              // setValue("code", obj.dialCode);
                            } else {
                              field.onChange("");
                              // setValue("code", "");
                            }
                            // setValue("phoneNumber", num, {
                            //   shouldValidate: true, // Validate the field after change
                            //   shouldDirty: true, // Mark the field as dirty
                            // });
                          }}
                        />
                        {error?.type === "required" && isSuperAdminMobileValid && (
                          <helperText className="text-danger">
                            <FormattedMessage id="thisfieldisrequired" />
                          </helperText>
                        )}
                        {!isSuperAdminMobileValid && (
                          <helperText className="text-danger">
                            <FormattedMessage id="please enter correct phone format" />
                          </helperText>
                        )}
                      </>
                    )}
                  />
                </div>
                <div className="col-md-6 my-2">
                  <Controller
                    name={"superAdminPassword"}
                    control={control}
                    rules={{
                      required: true,
                      pattern: strongPasswordRegex,
                    }}
                    render={({ field, fieldState }) => (
                      <>
                        <TextField
                          type={togglePassword ? "text" : "password"}
                          label={formatMessage({ id: "Password" })}
                          variant="outlined"
                          fullWidth
                          onChange={(e) => field.onChange(e.target.value)}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={() => {
                                    setTogglePassword((prev) => !prev);
                                  }}
                                  aria-label={togglePassword ? "Hide password" : "Show password"}
                                  edge="end"
                                >
                                  {togglePassword ? <Visibility /> : <VisibilityOff />}
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                        {errors?.superAdminPassword?.type === "required" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="thisfieldisrequired" />
                          </helperText>
                        )}
                        {errors?.superAdminPassword?.type === "pattern" && (
                          <helperText className="text-danger">
                            <FormattedMessage id="Input is not valid" />
                          </helperText>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>
            )}
          </div>

          {isEditMode || isAddMode ? (
            <div className="row justify-content-end">
              <div className="col-md-6">
                <div className="row justify-content-end">
                  <div className="col-md-3 mt-2">
                    <button
                      type="submit"
                      className="btn btn-primary text-center text-white"
                      disabled={
                        !isMobileValid
                          ? true
                          : !disabled
                          ? false
                          : CreateLoader ||
                            UpdateLoader ||
                            Object.keys(errors)?.length ||
                            (!isDirty && agencyId) ||
                            AgencyLoader
                      }
                    >
                      <FormattedMessage id="save" />
                    </button>{" "}
                  </div>
                  <div className="col-md-3 mt-2">
                    <button
                      onClick={() => history.goBack()}
                      type="button"
                      className="btn btn-danger text-white text-center"
                    >
                      <FormattedMessage id="cancel" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </form>
      ) : null}
    </div>
  );
}

export default memo(AddEdit);
