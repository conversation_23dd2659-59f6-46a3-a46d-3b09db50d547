import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { GetAgenciesDropdownQuery } from "gql/queries/Agency.queries.gql";

export default function AgenciesDropdown({ actionFunc, query, ...props }) {
  const { data, loading } = useQuery(GetAgenciesDropdownQuery);
  const { locale, formatMessage } = useIntl();
  const [selectedAgency, setSelectedAgency] = useState();

  const options =
    data?.agencies?.collection?.map((x) => ({
      value: x.id,
      label: x.name,
    })) || [];

  const selectInputRef = React.useRef();
  useEffect(() => {
    if (query?.agency) {
      setSelectedAgency(query.agency);
    }
  }, [query]);

  return (
    <Select
      className={`dropdown-select col-md-3 mt-1`}
      options={options}
      ref={selectInputRef}
      value={selectedAgency}
      isClearable
      loadOptions={loading}
      placeholder={formatMessage({ id: "Agency Name" })}
      onChange={(selection) => {
        actionFunc(selection);
        setSelectedAgency(selection);
      }}
      noOptionsMessage={() => {
        if (loading) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
