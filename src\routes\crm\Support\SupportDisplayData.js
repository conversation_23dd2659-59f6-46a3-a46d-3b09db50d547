import React, { useState, useEffect } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import InfoCard from "components/shared/InfoCard";
import PropTypes from "prop-types";
import moment from "moment";
import { Link } from "react-router-dom";
import RepliesCard from "components/shared/RepliesCard";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import IntlMessages from "util/IntlMessages";

export function SupportDisplayData({ feedbackDetails }) {
  const [feedback, setFeedback] = useState();
  const { locale } = useIntl();

  useEffect(() => {
    if (feedbackDetails) {
      const {
        id,
        feedbackType,
        feedbackAbout,
        feedbackTopic,
        topicReason,
        feedbackBank,
        accountFullName,
        accountIban,
        rental,
        status,
        createdAt,
        resolvingDate,
        attachment,
        description,
      } = feedbackDetails.feedbackDetails;

      const feedback = [
        { msgId: "Ticket.ID", value: id },
        { msgId: "Type", value: feedbackType ?  <FormattedMessage id={feedbackType} /> : null },

        { msgId: "Category.feedback", value:  feedbackAbout ? <FormattedMessage id={feedbackAbout} /> : null },
       
        {
          msgId: "Topic",
          value:feedbackTopic?.nameEn?.toLowerCase() == "other" ? topicReason : feedbackTopic[`name${locale.charAt(0)?.toUpperCase() + locale?.slice(1)}`],
        },
        {
            msgId: "Description",
            value: description ,
        },
        {
            msgId: "Bank.Name",
            value: feedbackBank?.name,
        },
        {
            msgId: "Full name of account",
            value: accountFullName,
        },
        {
            msgId: "IBAN",
            value: accountIban,
        },
        {
            msgId: "Booking No",
            value: rental?.bookingNo,
        },
        {
            msgId: "Status",
            value: <FormattedMessage id={status} />,
        },
        {
            msgId: "Customer.ID",
            value: <Link to={`/cw/dashboard/customers/${rental?.userId}`} >{rental?.userId}</Link> ,
        },
        {
            msgId: "Reporting date",
            value: moment(createdAt).locale(locale).format("LL") ,
        },
        {
            msgId: "Resolving.date",
            value: resolvingDate ? moment(resolvingDate).locale(locale).format("LL")  :null,
        },
        {
            image: attachment,
            imageDetails: {
              className: "img-responsive",
              containerClassName: "profile-userpic",
            },
          },
      ];
      setFeedback(feedback);
    }
  }, [feedbackDetails, locale]);

  return (
    <>
    <InfoCard
      fullwidth
      data={feedback}
      titleId="sidebar.Support"
        replies={
            feedbackDetails?.feedbackDetails?.feedbackReplies
        }
    />  
       <div className={`col-sm-12 col-md-${"12"} `}>
      <RctCollapsibleCard
        colClasses="col-sm-12 col-md-12 col-lg-12 w-xs-full p-0"
        heading={
          <div className="d-flex" style={{ gap: "7px", alignItems: "center" }}>
            <IntlMessages id={"replies"} />
          
          </div>
        }
        collapsible
        fullBlock
        customClasses="overflow-hidden"
      >
    <RepliesCard replies={  feedbackDetails?.feedbackDetails?.feedbackReplies} />

      </RctCollapsibleCard>
      </div>
    </>


  );
}
SupportDisplayData.propTypes = {
    feedbackDetails: PropTypes.object,
};  
