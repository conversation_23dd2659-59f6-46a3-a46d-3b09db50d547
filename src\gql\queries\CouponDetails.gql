query CouponDetails($id:ID!)
{
  couponDetails(id:$id) {
    allyCompanies {
      addedBy
      address
      allyClass
      allyRateId
      arName
      bankCardImage
      canHandoverInAntherCity
      commercialRegestration
      commercialRegistrationImage
      commisionRate
      conditions
      email
      enName
      features
      id
      isActive
      isApiIntegrated
      isB2b
      isB2c
      isDeliveryOnlinePay
      isOnlinePayEnable
      lat
      licenceImage
      lng
      logo
      lonlat
      managerName
      name
      officeNumber
    
      phoneNumber
      rate
      updatedBy
    }
    areas {
      arName
      
      centerLat
      centerLng
      enName
      id
      isAirport
      name
      order
      timezone
    }
    code
    forNewCustomers
    discountType
    discountTypeKey
    discountValue
    expireAt
    generalTerms
    id
    carVersions{
      id
    }
    branches{
      id
    }
    maxLimitValue
    paymentMethod
    minRentDays
    minRentPrice
    numOfDays
    numOfUsages
    numOfUsagesPerUser
    paymentBrands
    startAt
    agencies {
      id
      nameAr
      nameEn
      name
      isActive
    }
  }
}
