// Component is used for Carwah admin to add/edit agency and shared also in add/edit profile of Agency
import CustomTextField from "components/Input/CustomTextField";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import React, { useEffect, useRef, useState } from "react";
import { Helmet } from "react-helmet";
import { FormattedMessage, useIntl } from "react-intl";
import IntlMessages from "util/IntlMessages";
import { CreateAgencyUser, EditAgencyUser } from "gql/mutations/Agencies.gql";
import { useMutation, useQuery } from "@apollo/client";
import { Controller, useForm } from "react-hook-form";
import { emailPattern, strongPasswordRegex } from "constants/regex";
import IntlTelInput from "react-intl-tel-input";
import { AgencyUser } from "gql/queries/Agencies.gql";
import { IconButton, TextField, InputAdornment, Button } from "@material-ui/core";
import { Visibility, VisibilityOff } from "@material-ui/icons";
import { NotificationManager } from "react-notifications";
import { useHistory, useParams } from "react-router-dom";
import { userCan } from "functions";
import ReactSelect from "react-select";

export default function AgenciesUsersAddEdit() {
  const history = useHistory();
  const telephoneRef = useRef();
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const { userId } = useParams();
  const isEditMode = history.location.pathname.includes("edit");
  const isAddMode = history.location.pathname.includes("add");

  const agencyId = user_data?.agency_id || history?.location?.state?.agencyId;

  const { formatMessage } = useIntl();
  const [loader, setLoader] = useState(false);
  const [isMobileValid, setIsMobileValid] = useState(true);
  const [togglePassword, setTogglePassword] = useState(false);

  const options = [
    { value: "agency_admin", label: formatMessage({ id: "agency_admin" }) },
    {
      value: "agency_customer_service",
      label: formatMessage({ id: "agency_customer_service" }),
    },
  ];

  const {
    control,
    watch,
    handleSubmit,
    formState: { errors, isDirty, dirtyFields },
    setValue,
    reset,
  } = useForm({ mode: "all", reValidateMode: "onChange" });
  const [createAgency, { loading: CreateLoader }] = useMutation(CreateAgencyUser, {
    errorPolicy: "all",
  });
  const [updateAgency, { loading: UpdateLoader }] = useMutation(EditAgencyUser, {
    errorPolicy: "all",
  });
  // const [imageUpload] = useMutation(ImageUpload);
  const {
    data: agencyUserRes,
    refetch,
    loading: UserLoader,
  } = useQuery(AgencyUser, {
    skip: !userId,
    variables: {
      id: +userId,
      agencyId,
    },
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (agencyUserRes) {
      const userType = options.find((item) => item?.value == agencyUserRes?.agencyUser?.userType);
      reset({
        ...agencyUserRes.agencyUser,
        phoneNumber: agencyUserRes.agencyUser.user.mobile,
        userType: userType,
        user: undefined,
        __typename: undefined,
      });
    }
  }, [agencyUserRes]);

  const onSubmit = (data) => {
    const payload = {
      variables: {
        input: {
          ...data,
          phoneNumber: `${data.phoneNumber}`,
          agencyId: agencyId,
          isActive: true,
          id: userId || undefined,
          userType:
            data?.userType?.value === "agency_super_admin" ||
            agencyUserRes?.agencyUser?.userType === "agency_super_admin"
              ? undefined
              : data.userType.value,
          code: undefined,
        },
      },
    };
    if (userId) {
      updateAgency(payload)
        .then((res) => {
          if (res?.errors) {
            res.errors.map(({ message }) => {
              NotificationManager.error(message);
            });
            return;
          }
          NotificationManager.success(<FormattedMessage id="Edited Successfully" />);
          history.push("/cw/dashboard/users");
        })
        .catch((error) => {
          NotificationManager.error(error?.message);
        });
      return;
    }

    createAgency(payload)
      .then((res) => {
        if (res?.errors) {
          res.errors.map(({ message }) => {
            NotificationManager.error(message);
          });
          return;
        }
        NotificationManager.success(<FormattedMessage id={"AddSuccessfully"} />);
        history.push("/cw/dashboard/users");
      })
      .catch((error) => {
        NotificationManager.error(error?.message);
      });
  };

  return (
    <div className="clients-wrapper">
      <Helmet>
        <title>
          {formatMessage({
            id: isEditMode ? "Edit User" : "Add User",
          })}
        </title>
      </Helmet>

      <PageTitleBar
        title={<IntlMessages id={isEditMode ? "Edit User" : "Add User"} />}
        match={location}
        lastElement={
          isEditMode ? userId : <IntlMessages id={isEditMode ? "Edit User" : "Add User"} />
        }
        enableBreadCrumb
        extraButtons={
          <>
            {userCan("agencies.manage_users") && !isEditMode && (
              <Button
                variant="contained"
                color="primary"
                className="btn btn-success"
                onClick={() => history.push("users/edit")}
              >
                <IntlMessages id="Edit" />
              </Button>
            )}
          </>
        }
      />
      {!isEditMode || (isEditMode && !UserLoader) ? (
        <form
          onSubmit={handleSubmit(onSubmit)}
          autoComplete="off"
          // key={agency}
        >
          <div className="row mt-5 pt-3">
            <div className="col-md-12 d-block mb-2 ">
              <h4 style={{ fontWeight: "bold" }}>
                <FormattedMessage id="User Details" />
              </h4>
            </div>
            <div className="col-md-6 my-2">
              <Controller
                name={"firstName"}
                control={control}
                rules={{
                  required: true,
                  minLength: 1,
                  maxLength: 300,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      value={watch("firstName")}
                      fullWidth
                      onchange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "First Name" })}
                      label={formatMessage({ id: "First Name" })}
                      error={errors?.firstName}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.firstName?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.firstName?.type === "minLength" ||
                      (errors?.firstName?.type === "maxLength" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="Input is not valid" />
                        </helperText>
                      ))}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2">
              <Controller
                name={"lastName"}
                control={control}
                rules={{
                  required: true,
                  minLength: 1,
                  maxLength: 300,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      fullWidth
                      value={watch("lastName")}
                      onchange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Last Name" })}
                      label={formatMessage({ id: "Last Name" })}
                      error={errors?.lastName}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.lastName?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.lastName?.type === "minLength" ||
                      (errors?.lastName?.type === "maxLength" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="Input is not valid" />
                        </helperText>
                      ))}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2">
              <Controller
                defaultValue={agencyUserRes?.agencyUser?.email}
                name={"email"}
                control={control}
                rules={{
                  required: true,
                  pattern: emailPattern,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <CustomTextField
                      defaultValue={watch("email")}
                      fullWidth
                      onblur={(e) => {
                        field.onChange(e.target.value);
                      }}
                      type="text"
                      placeholder={formatMessage({ id: "Email" })}
                      label={formatMessage({ id: "Email" })}
                      error={errors?.email}
                      disabled={!isEditMode && !isAddMode}
                    />
                    {errors?.email?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.email?.type === "pattern" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="validation.emailFormatPleaseFormat" />
                      </helperText>
                    )}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2" dir="ltr">
              <Controller
                name={"phoneNumber"}
                control={control}
                defaultValue={agencyUserRes?.agencyUser?.user?.mobile?.replaceAll(" ", "") || ""}
                rules={{
                  required: true,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <IntlTelInput
                      ref={telephoneRef}
                      fieldId="input-tel"
                      separateDialCode
                      telInputProps={{ pattern: "[0-9]*" }}
                      preferredCountries={[
                        /^(966|9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/.test(field.value) ||
                        !isEditMode
                          ? "sa"
                          : "eg",
                      ]}
                      containerClassName="intl-tel-input"
                      // placeholder="512345678*"
                      defaultValue={field.value} // Keep the value in sync
                      onPhoneNumberBlur={(isValid, num, obj, fullNum) => {
                        isDirty && setIsMobileValid(isValid);
                        if (isValid) {
                          field.onChange(fullNum?.replaceAll(" ", "")); // Update the form value
                          // setValue("code", obj.dialCode);
                        } else {
                          field.onChange("");
                          // setValue("code", "");
                        }
                        // setValue("phoneNumber", num, {
                        //   shouldValidate: true, // Validate the field after change
                        //   shouldDirty: true, // Mark the field as dirty
                        // });
                      }}
                      // onPhoneNumberChange={}
                      disabled={!isEditMode && !isAddMode}
                      nationalMode={false} // Disable national mode
                      autoFormat={false} // Prevent automatic formatting
                      formatOnDisplay={false} // Prevent display formatting
                      format={false}
                      formatOnInit={false}
                    />
                    {errors?.phoneNumber?.type === "required" && isMobileValid && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {!isMobileValid && (
                      <helperText className="text-danger">
                        <FormattedMessage id="please enter correct phone format" />
                      </helperText>
                    )}
                  </>
                )}
              />
            </div>
            <div className="col-md-6 my-2">
              <Controller
                // key={}
                name={"userType"}
                control={control}
                rules={{
                  required: agencyUserRes?.agencyUser?.userType !== "agency_super_admin",
                }}
                render={({ field, fieldState }) => {
                  if (agencyUserRes?.agencyUser?.userType === "agency_super_admin") {
                    return (
                      <CustomTextField
                        value={formatMessage({ id: "agency_super_admin" })}
                        fullWidth
                        type="text"
                        placeholder={formatMessage({ id: "Role" })}
                        label={formatMessage({ id: "Role" })}
                        disabled={true}
                      />
                    );
                  }
                  return (
                    <>
                      <ReactSelect
                        options={options}
                        isClearable
                        // loadOptions={getCompanies || loading}
                        defaultValue={options.find(
                          (optn) => `${optn?.value}` === `${watch("userType")?.value}`,
                        )}
                        value={options.find(
                          (optn) => `${optn?.value}` === `${watch("userType")?.value}`,
                        )}
                        placeholder={formatMessage({ id: "Role" })}
                        onChange={(selection) => {
                          field.onChange(selection);
                        }}
                        noOptionsMessage={() => {
                          if (!options?.length) return "no data found";
                        }}
                      />
                      {errors?.userType?.type === "required" && (
                        <helperText className="text-danger">
                          <FormattedMessage id="thisfieldisrequired" />
                        </helperText>
                      )}
                    </>
                  );
                }}
              />
            </div>

            <div className="col-md-6 my-2">
              <Controller
                name={"password"}
                control={control}
                rules={{
                  required: !isEditMode,
                  pattern: strongPasswordRegex,
                }}
                render={({ field, fieldState }) => (
                  <>
                    <TextField
                      type={togglePassword ? "text" : "password"}
                      label={formatMessage({ id: "Password" })}
                      variant="outlined"
                      fullWidth
                      onChange={(e) => field.onChange(e.target.value)}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => {
                                setTogglePassword((prev) => !prev);
                              }}
                              aria-label={togglePassword ? "Hide password" : "Show password"}
                              edge="end"
                            >
                              {togglePassword ? <Visibility /> : <VisibilityOff />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                    {errors?.password?.type === "required" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="thisfieldisrequired" />
                      </helperText>
                    )}
                    {errors?.password?.type === "pattern" && (
                      <helperText className="text-danger">
                        <FormattedMessage id="Input is not valid" />
                      </helperText>
                    )}
                  </>
                )}
              />
            </div>
          </div>

          {isEditMode || isAddMode ? (
            <div className="row justify-content-end">
              <div className="col-md-6">
                <div className="row justify-content-end">
                  <div className="col-md-3 mt-2">
                    <button
                      type="submit"
                      className="btn btn-primary text-center text-white"
                      disabled={
                        CreateLoader ||
                        UpdateLoader ||
                        Object.keys(errors)?.length ||
                        (!isDirty && userId) ||
                        UserLoader
                      }
                    >
                      <FormattedMessage id="save" />
                    </button>{" "}
                  </div>
                  <div className="col-md-3 mt-2">
                    <button
                      onClick={() => history.goBack()}
                      type="button"
                      className="btn btn-danger text-white text-center"
                    >
                      <FormattedMessage id="cancel" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </form>
      ) : null}
    </div>
  );
}
