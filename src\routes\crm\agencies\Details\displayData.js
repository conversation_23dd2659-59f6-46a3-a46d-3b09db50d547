/* eslint-disable prettier/prettier */
import React, { useState, useEffect } from "react";
import { useIntl } from "react-intl";
import InfoCard from "components/shared/InfoCard";
import PropTypes from "prop-types";
import moment from "moment";

export function AgencyDisplayData({ agencyProfile,allyCompanies }) {
  const [agencyDetails, setAgencyDetails] = useState();
  const [allys,setAllys]=useState([])
  const { locale } = useIntl();
  useEffect(() => {
    if (agencyProfile) {
      const {id,nameAr,nameEn,email,phoneNumber,vatRegistrationId,isActive,createdAt,agencyKey,logo } = agencyProfile?.agency;

      const agencyprofileDetails = [
        { msgId: "agency.id" , value: id },
        { msgId:"agency.name" , value: locale =="ar" ? nameAr :nameEn  },
        { msgId: "Agency Key", value: agency<PERSON><PERSON> },
        { msgId: "email" , value: email },
        { msgId: "Phone" , value: phoneNumber },

        

        // { msgId: "Ally", value: carMoodel?.vehicleType[`${locale}Name`] },
        { msgId: "Status", value: isActive },
        { msgId: "Vat Registration ID", value: vatRegistrationId },
                { msgId: "Creation date" , value:  moment(createdAt).locale(locale).format("LL hh:mm A") },
                {
                  image: logo,
                  imageDetails: {
                    className: "img-responsive",
                    containerClassName: "profile-userpic",
                  },
                }
      ];
      setAgencyDetails(agencyprofileDetails)
    }
  }, [agencyProfile, locale]);

  return <InfoCard fullwidth data={agencyDetails} allyCompanies={allyCompanies} titleId="agency.details" />;
}
AgencyDisplayData.propTypes = {
  versionprofile: PropTypes.object, 
};
