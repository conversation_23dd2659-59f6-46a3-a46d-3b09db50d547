<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="apple-itunes-app" content="app-id=1387161496" />
    <meta name="google-play-app" content="app-id=com.carwah.allie" />

    <link
      rel="apple-touch-icon"
      href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
    />
    <link
      rel="android-touch-icon"
      href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
    />
    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
    <link rel="shortcut icon" href="favicon.ico" />
    <!-- Use For Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    <!-- Use For Material Iconic Font -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/material-design-iconic-font/2.2.0/css/material-design-iconic-font.min.css"
    />
    <!-- Use For Simple Line Icon -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.css"
    />
    <!-- Use For Leaflet Map -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.1.0/leaflet.css"
    />
    <!-- Use For Jvector Map -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/jvectormap/2.0.4/jquery-jvectormap.css"
      type="text/css"
      media="screen"
    />
    <!-- Use For Google Font -->
    <link
      href="https://fonts.googleapis.com/css?family=Heebo:100,300,400,500,700,800,900"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="manifest" href="/site.webmanifest" />
              <meta name="apple-itunes-app" content="app-id=1387161215" />
          <meta name="google-play-app" content="app-id=com.carwah.customer" />

          <link
            rel="apple-touch-icon"
            href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
          />
          <link
            rel="android-touch-icon"
            href="https://play-lh.googleusercontent.com/MHLbpTa2U4wwZDLJ7Iu4WiWcXkQyhAtBsNAoHFoHyoKBntFz0K0NMVzq6W2nZKqVTFM=w240-h480-rw"
          />

    <!-- <script src="https://use.fontawesome.com/983712cd0a.js"></script> -->
    <!-- <script src="https://kit.fontawesome.com/8b3beab2ce.js" crossorigin="anonymous"></script> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css">

    <!-- <link rel="stylesheet" href="@sweetalert2/themes/dark/dark.css"> -->
    <!-- <script src="sweetalert2/dist/sweetalert2.min.js"></script> -->
    <!--
  
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    
    <title>Carwah</title>
  </head>

  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
