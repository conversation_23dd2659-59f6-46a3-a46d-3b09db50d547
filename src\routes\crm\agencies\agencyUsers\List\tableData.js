/* eslint-disable prettier/prettier */
import React from "react";
import { dataTypes } from "constants/constants";
import { Link } from "react-router-dom";
import moment from "moment";

const { TEXT, ACTIONS, FUNC } = dataTypes;
export const Data = [
  {
    headerId: "User Name",
    dataType: FUNC,
    func: (record, locale) => (
      <Link to={`/cw/dashboard/users/${record.user.id}`}>{record[`name`]}</Link>
    ),
  },
  {
    headerId: "email",
    dataRef: "email",
    dataType: TEXT,
  },
  {
    headerId: "phoneNumber",
    dataType: FUNC,
    func: (record, locale, refetch, formatMessage) => <span>{record.user.mobile}</span>,
  },
  {
    headerId: "Role",
    dataType: FUNC,
    func: (record, locale, refetch, formatMessage) => (
      <span>{formatMessage({ id: record[`userTypeLocalized`] })}</span>
    ),
  },
  {
    headerId: "status",
    dataType: FUNC,
    func: (record, locale, refetch, formatMessage) =>
      formatMessage({ id: record.isActive ? "Active" : "inactive" }),
  },
  { headerId: "common.actions", dataType: ACTIONS },
];
