query AgencyUsers($agencyId: ID, $name: String, $email: String, $limit: Int, $page: Int) {
  agencyUsers(agencyId: $agencyId, name: $name, email: $email, limit: $limit, page: $page) {
    collection {
      createdAt
      agencyId
      name
      email
      isActive
      userType
      userTypeLocalized
      user {
        id
        mobile
      }
    }
    metadata {
      currentPage
      limitValue
      totalCount
      totalPages
    }
  }
}
