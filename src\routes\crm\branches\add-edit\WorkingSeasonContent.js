/* eslint-disable prettier/prettier */
/* eslint-disable no-undefined */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from "reactstrap";
import { FormattedMessage, useIntl } from "react-intl";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import { useLazyQuery, useMutation, useQuery } from "@apollo/client";
import { UpdateBranchWorkingSeason} from "gql/mutations/UpdateWorkingSeason.gql";
import {ApplyBranchWorkingSeason} from "gql/mutations/ApplySeason.gql"
import {DeleteBranchWorkingSeason} from "gql/mutations/DeleteWorkingSeason.gql"

import TextField from "@material-ui/core/TextField";
import {
    workingDays,
    workingDaysIndeeces,
  } from "constants/constants";
  import {
    Checkbox,
    Switch,
    FormLabel
  } from "@material-ui/core";
import IconButton from "@material-ui/core/IconButton";
import {
    DeleteForever as DeleteForeverIcon,
    Add as AddIcon,
    ClearAll as ClearIcon,
  
  } from "@material-ui/icons";
import moment from "moment";
import { useSnackbar } from "notistack";
import CustomTextField from "components/Input/CustomTextField";
import { useParams } from "react-router-dom/cjs/react-router-dom.min";
import { NotificationManager } from "react-notifications";

const WorkingSeasonsContent = (props) => {
  const {
    openmodel,
    setOpenModel,
    season,
    reftechBranch,
    Seasons,
    setSeasonsAttribute,
    index,
    setChanged
  } = props;
  const [updateBranchWorkingSeason, { loading: gWorkingSeason,  }] =
  useMutation(UpdateBranchWorkingSeason);
  const [applyBranchWorkingSeason, { loading: ApplyLoader  }] =
  useMutation(ApplyBranchWorkingSeason);
  const [deleteBranchWorkingSeason, { loading: DeleteLoader  }] =
  useMutation(DeleteBranchWorkingSeason);
  
  
  const { locale, formatMessage } = useIntl();
  const [deliveryGraceTime, setDeliveryGraceTime] = useState(season.deliveryGraceTime);
  const [isDisabled, setIsDisabled] = useState(false);
  const [seasonName,setSeasonName]=useState()
  const [seasonWorkingDaysAttributes,setSeasonWorkingDaysAttributes]=useState([])
  const { branchId } = useParams();

  const { enqueueSnackbar } = useSnackbar();

  const toggle = () => setOpenModel(!openmodel);
  const Cancel = () => {
    toggle();
  };
  const [workingHours, setworkingHours] = useState(
    arrangeData( season.seasonWorkingDaysAttributes)
   
  );
  useEffect(() => {
    setSeasonWorkingDaysAttributes(workingHours.flat())
    const SeasonsCopy=[...Seasons]
    SeasonsCopy[index]={
      ...SeasonsCopy[index],
      seasonWorkingDaysAttributes:workingHours.flat()
    }
    setSeasonsAttribute(SeasonsCopy)   
    setChanged(true)
    // setFieldValue("branchWorkingDayAttributes", workingHours.flat());
  }, [workingHours]);
  useEffect(()=>{
    setSeasonName(season.seasonName)
    
  },[season])

  function handleTime(value, type) {
    const numericValue = Number(value);
    const hrs = deliveryGraceTime.split(":")[0];
    const mins = deliveryGraceTime.split(":")[1];
    setChanged(true)
    if (type === "hrs") {
      if (numericValue < 10) {
        setDeliveryGraceTime(`0${numericValue}:${mins}`);
        const SeasonsCopy=[...Seasons]
    SeasonsCopy[index]={
      ...SeasonsCopy[index],
      deliveryGraceTime:`0${numericValue}:${mins}`
    }
    setSeasonsAttribute(SeasonsCopy) 
      } else {
        
        setDeliveryGraceTime(`${numericValue}:${mins}`);
        const SeasonsCopy=[...Seasons]
        SeasonsCopy[index]={
          ...SeasonsCopy[index],
          deliveryGraceTime:`0${numericValue}:${mins}`
        }
        setSeasonsAttribute(SeasonsCopy) 
      }
    } else if (type === "mins") {
      if (numericValue < 10) {
        setDeliveryGraceTime(`${hrs}:0${numericValue}`);
        const SeasonsCopy=[...Seasons]
        SeasonsCopy[index]={
          ...SeasonsCopy[index],
          deliveryGraceTime:`${hrs}:0${numericValue}`
        }
        setSeasonsAttribute(SeasonsCopy) 
      } else {
        setDeliveryGraceTime(`${hrs}:${numericValue}`);
        const SeasonsCopy=[...Seasons]
        SeasonsCopy[index]={
          ...SeasonsCopy[index],
          deliveryGraceTime:`${hrs}:${numericValue}`
        }
        setSeasonsAttribute(SeasonsCopy) 
      }
    }
  }
  function arrangeData(data){
    const weekdayMap = {};

    // To store the final result
    const result = [];
    
    // Group by weekday
    data.forEach(obj => {
      if (weekdayMap[obj.weekDay]) {
        weekdayMap[obj.weekDay].push(obj);
      } else {
        weekdayMap[obj.weekDay] = [obj];
      }
    });
    workingDaysIndeeces.forEach(obj=>{
      if(!weekdayMap[obj]){
        weekdayMap[obj]=[

          {
            branchWorkingSeasonId: undefined,
            endTime: "13:00",
            is24hAvailable: false,
            isOn: false,
            startTime: "12:00",
             weekDay: obj
          }
        ]
      }
    })
    // Separate repeated and non-repeated
    for (const weekday in weekdayMap) {
      if (weekdayMap[weekday].length > 1) {
        // Push repeated objects as an array
        result.push(weekdayMap[weekday]);
      } else {
        // Push non-repeated objects directly
        result.push([weekdayMap[weekday][0]]);
      }
    }
    return(result);
  }
  function ValidateShift({ startTime, endTime, is24hAvailable }) {
    setChanged(true)
    const shiftStart = moment(startTime, "HH:mm");
    const shiftEnd = moment(endTime, "HH:mm");
    const differenceBtwShiftsInMinutes = moment.duration(shiftEnd.diff(shiftStart)).asMinutes();
    const graceTimeInMinutes = moment.duration(deliveryGraceTime).asMinutes();
    if (is24hAvailable) {
      return ""; // Skips validation if shift is 24hrs
    }
    if (differenceBtwShiftsInMinutes <= graceTimeInMinutes * 2) {
      setIsDisabled(true);
      return <p style={{ color: "red" }}>{formatMessage({ id: "shifts_grace_validation" })}</p>;
    }
    setIsDisabled(false);
    return "";
  }

 const  handelApplySeason =()=>{
  if(branchId){
    applyBranchWorkingSeason({
      variables:{
        branchWorkingSeasonId: Seasons[index].seasonId
      }
    }).then((res)=>{
      if(res.data.applyBranchWorkingSeason?.errors?.length){
    NotificationManager.error(<FormattedMessage id={res.data.applyBranchWorkingSeason?.errors} />)
        
      }else{
        NotificationManager.success(<FormattedMessage id="apply.saeson.success" />)
        reftechBranch()
      }
   
    })
  }else{
    const SeasonsCopy=[...Seasons]
    const AllInactiveSeasons=SeasonsCopy.map((item)=>({
      ...item,
      isActive:false
    }))
    AllInactiveSeasons[index].isActive=true
  
    setSeasonsAttribute(AllInactiveSeasons)
    NotificationManager.success(<FormattedMessage id="apply.saeson.success" />)
  }

  setChanged(true)
 }
 const  DeleteSeason =()=>{
  if(branchId){
    deleteBranchWorkingSeason({
      variables:{
        branchWorkingSeasonId:season.seasonId
      }
    }).then((res)=>{
      if(res.data.deleteBranchWorkingSeason?.errors?.length){
        NotificationManager.error(res.data.deleteBranchWorkingSeason?.errors) 
      }else{
        reftechBranch()
        NotificationManager.success(<FormattedMessage id="season.delete.success" />)
      }
    }).catch(error=> NotificationManager.error(error.message))
  }else{
    const SeasonsCopy=[...Seasons]
    const filteredSeasons=SeasonsCopy.filter((season,idx)=>index != idx)
     setSeasonsAttribute(filteredSeasons)
     NotificationManager.success(<FormattedMessage id="delete.saeson.success" />)
  }
  setChanged(true)

 }
 function validateIfShiftsExists(e, dayIndex, index, isStartTime){
  setChanged(true)
  const prevShiftEnd = moment(
    workingHours[dayIndex][index - 1]?.endTime,
    "HH:mm",
  );
  const isSelectedTimeAlreadyExists = [...workingHours[dayIndex]]?.filter(shift => {
    return  shift.startTime === e.target.value || shift.endTime === e.target.value;
  });
  if(isSelectedTimeAlreadyExists?.length || isStartTime && 
    moment(e.target.value, "HH:mm").diff(prevShiftEnd) < 0){
    enqueueSnackbar(
      formatMessage({
        id: "Time overlapping is not allowed for different shifts on a day",
      }),
      {
        preventDuplicate: true,
        variant: "info",
      },
    );
  };

  return Boolean(isSelectedTimeAlreadyExists?.length || isStartTime && 
    moment(e.target.value, "HH:mm").diff(prevShiftEnd) < 0);
  
}
  return (
    <div>
        <div className="row">
          <div className="w-100">
            <div className="col-md-4">
            <CustomTextField
                fullWidth
                name="seasonName"
                onChange={(e)=>{
                  setSeasonName(e.target.value)
                  const AllSeasons=[...Seasons]
                  AllSeasons[index].seasonName=e.target.value
                  setSeasonsAttribute(AllSeasons)
                  setChanged(true)
                }}

                value={seasonName}
                // onChange={(e) => setFieldValue("districtNameAr", e.target.value)}
                // error={!!submitCount && Boolean(errors.districtNameAr)}
                // errormsg={!!submitCount && errors.districtNameAr}
              />
            </div>
         
          </div>
            <div className="w-50">
              
              {workingDaysIndeeces.map((day, dayIndex) => (
                
                <div className="mx-4 px-2">
                  <strong>{formatMessage({ id: workingDays[dayIndex] })}</strong>
                  <FormControlLabel
                    control={<Checkbox name="jason" />}
                    label={<FormattedMessage id="24.hours" />}
                    style={{ minWidth: "100px", marginLeft: "10px" }}
                    checked={workingHours[day]?.[0]?.is24hAvailable ?  workingHours[day]?.[0]?.is24hAvailable : false}
                    onChange={(e) => {
                      const obj = {
                        isOn: e.target.checked,
                        weekDay: day,
                        startTime: "00:00",
                        endTime: "00:00",
                        is24hAvailable: e.target.checked,
                      };

                      const update = [...workingHours];
                      if (e.target.checked) {
                        const updated = update.map((updateone, index) =>
                          day == index ? [updateone[0]] : updateone,
                        );
                        updated[day][0] = obj;
                        setworkingHours([...updated]);
                      } else {
                        update[day][0] = obj;
                        setworkingHours(update);
                      }
                    }}
                  />
                  {workingHours?.[day]?.map((workTimes, index, arr) => (
                    <>
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="w-60 ">
                          <TextField
                            disabled={workTimes?.is24hAvailable}
                            id="time"
                            label={formatMessage({ id: "starttime" })}
                            type="time"
                            dir="ltr"
                            value={workTimes?.is24hAvailable ? "00:00" : workTimes?.startTime}
                            InputLabelProps={{ shrink: true }}
                            onChange={(e) => {
                              if(!validateIfShiftsExists(e, day, index, true)){
                                const obj = { ...workTimes, startTime: e.target.value };
                                const update = [...workingHours];
                                update[day][index] = obj;
                                setworkingHours(update);
                              }else{
                                const obj = { ...workTimes, startTime: e.target.value ,endTime:"--"};
                                const update = [...workingHours];
                                update[day][index] = obj;
                                setworkingHours(update);
                              }
                            }}
                            inputProps={{ step: 1800 }}
                          />
                        </div>
                        {workTimes?.startTime && (
                          <div className="w-60">
                            <TextField
                              disabled={workingHours[day][index]?.is24hAvailable}
                              id="time"
                              label={formatMessage({ id: "endtime" })}
                              type="time"
                              dir="ltr"
                              value={workTimes?.is24hAvailable ? "23:59" : workTimes?.endTime}
                              InputLabelProps={{ shrink: true }}
                              onChange={(e) => {
                                if(!validateIfShiftsExists(e, day, index, false)){
                                  const obj = { ...workTimes, endTime: e.target.value };
                                  const update = [...workingHours];
                                  update[day][index] = obj;
                                  setworkingHours(update);
                                }
                              }}
                              inputProps={{ step: 1800 }}
                            />
                          </div>
                        )}
                        {workTimes?.endTime && (
                          <div className="d-flex align-items-center mt-4">
                            {/* disables delete if there is only one shift */}
                            {workingHours?.[day].filter((i) => i.startTime && i.endTime)
                              ?.length > 1 && (
                              <DeleteForeverIcon
                                className="mr-3 ml-3 pointer"
                                onClick={() => {
                                  const update = [...workingHours];
                                  update[day] = [...update[day]].filter(
                                    (a, idx) => idx !== index,
                                  );
                                  setworkingHours(update);
                                }}
                              />
                            )}

                            <IconButton
                              color="secondary "
                              onClick={() => {
                                const update = [...workingHours].map((arr, idx) =>
                                  +idx === +day
                                    ? [
                                        ...arr,
                                        {
                                          isOn: true,
                                          weekDay: day,
                                          endTime: "",
                                          startTime: "",
                                          is24hAvailable:false
                                           
                                        },
                                      ]
                                    : arr,
                                );
                                // return
                                setworkingHours(update);
                              }}
                              disabled={workingHours[day][index]?.is24hAvailable}
                              aria-label="upload picture"
                              component="span"
                            >
                              <AddIcon className="mr-3 ml-3 pointer" />
                            </IconButton>
                            <Switch
                              className="mr-2 ml-2"
                              color="primary"
                              checked={workTimes.isOn}
                              onChange={(e) => {
                                const obj = { ...workTimes, isOn: e.target.checked };
                                const update = [...workingHours];
                                update[day][index] = obj;
                                setworkingHours(update);
                                setChanged(true)
                              }}
                              name="toggle"
                              inputProps={{ "aria-label": "toggle" }}
                              onChange={(e) => {
                                const obj = { ...workTimes, isOn: e.target.checked };
                                const update = [...workingHours];
                                update[day][index] = obj;
                                setworkingHours(update);
                              }}
                            />
                          </div>
                        )}
                      </div>
                      <ValidateShift
                        startTime={workTimes?.startTime}
                        endTime={workTimes?.endTime}
                        is24hAvailable={workTimes?.is24hAvailable}
                      />
                    </>
                  ))}
                </div>
              ))}
               <div className="row m-4 px-4">
                <FormLabel component="legend">
                  <FormattedMessage id="Delivery Grace Time" />
                </FormLabel>
                <div className="mt-2">
                  <div className="d-flex" style={{ gap: "10px" }}>
                    <input
                      type="number"
                      min="0"
                      max="23"
                      value={deliveryGraceTime.split(":")[0]}
                      onChange={(e) => handleTime(e.target.value, "hrs")}
                      onKeyPress={(e) => e.preventDefault()}
                      onPaste={(e) => e.preventDefault()}
                    />
                    <span>:</span>
                    <input
                      type="number"
                      min="0"
                      max="59"
                      value={deliveryGraceTime.split(":")[1]}
                      onChange={(e) => handleTime(e.target.value, "mins")}
                      onKeyPress={(e) => e.preventDefault()}
                      onPaste={(e) => e.preventDefault()}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="row">
          <div className="pt-25 text-right d-flex justify-content-end">
      
        <div>
      
        
        <button
        disabled={season.isActive}
        type="button"
        className="btn btn-danger mr-15 text-white"
     onClick={()=>{
      DeleteSeason()
     }}
      >
        <FormattedMessage id="button.delete" />
      </button>
    
      
      {
   
        <button
        type="button"
        className="btn btn-info mr-15 text-white"
        disabled={season.isActive}
     onClick={()=>{
      handelApplySeason()
     }}
      >
        <FormattedMessage id="button.apply" />
      </button>
   
      }
         
        </div>
     
      </div>
        
       
      </div>
    </div>
  );
};

export default WorkingSeasonsContent;
