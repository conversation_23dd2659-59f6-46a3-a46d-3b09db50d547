@media screen and (min-width: 901px) {
  .rct-sidebar .site-logo {
    padding-top: 20px;
  }
}

@media screen and (max-width: 900px) {
  .rct-sidebar .site-logo {
    padding-top: 20px;
  }
}

@media screen and (min-width: 901px) {
  .MuiToolbar-regular {
    min-height: 48px !important;
  }
}

.rct-footer {
  margin-top: 20px;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.navbar-right li {
  display: block !important;
}

@media screen and (max-width: 900px) {
  .grid-insurance-cars {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;
    width: 100%;
    margin: 0;
  }

  .ecom-dashboard-wrapper>.row {
    display: block !important;
  }

  .ecom-dashboard-wrapper>.row .w-50,
  .ecom-dashboard-wrapper>.row .w-60 {
    width: 100% !important;
  }
}

.add-edit-coupons .invisible {
  display: none;
}

.add-edit-coupons p.visible {
  margin-bottom: 0 !important;
}

.rct-sidebar .rct-sidebar-nav .rct-mainMenu>li:not(.list-item) a {
  padding: 1rem !important;
}

.MuiTableSortLabel-icon {
  opacity: 0.4 !important;
}

.MuiCollapse-wrapperInner a {
  width: 100%;
}

.swal-text {
  text-align: start;
}

.swal-footer {
  text-align: center;
}

.table {
  background-color: #fff !important;
}

.table th,
.table td {
  white-space: nowrap !important;
  padding: 0.4rem !important;
  font-size: 13px !important;
}

.table th p,
.table td p {
  margin-bottom: 0 !important;
}

.table th,
.table th div,
.table td,
.table td div {
  white-space: nowrap !important;
}

.MuiTab-wrapper .MuiBadge-root {
  margin: 0 !important;
}

.MuiTab-labelIcon {
  padding: 7px 14px !important;
  min-height: 50px !important;
}

.MuiTab-root {
  min-width: -moz-max-content !important;
  min-width: max-content !important;
}

.rct-page-content {
  padding: 12px 24px !important;
}

.page-title {
  margin: 0px 5px !important;
  margin-bottom: 10px !important;
  flex-wrap: wrap;
}

.page-title button {
  margin-top: -5px;
}

@media (min-width: 600px) {
  .MuiToolbar-regular {
    min-height: 38px !important;
  }
}

.humburger,
.header-icon {
  border: none !important;
}

.clients-wrapper .MuiTypography-body1 {
  padding-top: 0px !important;
  margin-top: 0px !important;
}

.clients-wrapper>div:nth-child(2) {
  position: relative;
  z-index: 1;
}

.language-icon img {
  transform: translateY(-2px);
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  margin: -3px;
}

.MuiTab-wrapper {
  font-size: 0.85rem !important;
}

.page-title .breadcrumb {
  font-size: 14px !important;
  padding: 0px !important;
  scale: 0.9 !important;
  margin: 5px 0px !important;
}

.page-title-wrap h2 {
  font-size: 1.6rem !important;
  width: -moz-max-content;
  width: max-content;
}

.rct-block .rct-block-content {
  padding: 7px !important;
}

.badge:not(.badge-xs) {
  padding: 5px 10px 5px 10px !important;
}

span.MuiSwitch-root {
  scale: 0.7;
  margin: -15px;
}

.MuiTab-textColorPrimary.Mui-selected {
  font-weight: bold;
}

.MuiTabs-indicator {
  display: none;
}

.makes-select {
  display: table !important;
}

#side-bar-wrapper-collapsed>div>div:first-child {
  transform: translateX(0) !important;
  -webkit-transform: translateX(0) !important;
  -moz-transform: translateX(0) !important;
  -ms-transform: translateX(0) !important;
  -o-transform: translateX(0) !important;
}

#side-bar-wrapper-collapsed>div>div:first-child .MuiCollapse-root.MuiCollapse-entered {
  display: none;
}

#side-bar-wrapper-collapsed>div>div:last-child {
  inset: 0px 0px 0px 80px !important;
}

#side-bar-wrapper-collapsed>div>div:nth-child(2) {
  background: none !important;
}

#side-bar-wrapper-collapsed .rct-sidebar-content .rct-mainMenu .menu {
  display: none;
}

#side-bar-wrapper-collapsed .rct-sidebar-content,
#side-bar-wrapper-collapsed .rct-sidebar {
  width: -moz-min-content;
  width: min-content;
}

#side-bar-wrapper-collapsed .rct-mainMenu li {
  text-align: center !important;
}

#side-bar-wrapper-collapsed .site-logo {
  padding: 0.1em 0.8rem;
}

body.rtl #side-bar-wrapper-collapsed>div>div:last-child {
  inset: 0px 80px 0 0px !important;
}

body.rtl #side-bar-wrapper-collapsed .rct-scroll>div:first-child {
  margin-left: -18px !important;
}

/*# sourceMappingURL=override.css.map */
div[role=navigation] div:last-child {
  background-color: transparent !important;
}