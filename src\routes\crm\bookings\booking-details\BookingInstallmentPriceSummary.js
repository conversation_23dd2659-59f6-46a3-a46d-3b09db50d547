/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
import React from "react";
import PropTypes from "prop-types";
import { ListItem, CircularProgress } from "@material-ui/core";
import { FormattedMessage, useIntl } from "react-intl";
import RiyalComponent from "components/shared/RiyalComponent";

function BookingInstallmentPriceSummary({
  BookingPrice,
  calculatingPrice,
  insurance,
  handoverChecked,
  BookingDetails,
  isUnlimited,
  inBookingDetails,
  bookingType,
  plan,
  change = false,
}) {
  const aboutPrice = BookingPrice?.rentalAboutPrice || {};

  const {
    totalAddsPrice,
    deliveryPrice,
    discountPercentage,
    discountType,
    discountValue,
    insuranceIncluded,
    insuranceValue,
    numberOfDays,
    priceBeforeDiscount,
    priceBeforeInsurance,
    pricePerDay,
    dailyPrice,
    taxValue,
    totalPrice,
    valueAddedTaxPercentage,
    priceBeforeTax,
    handoverPrice,
    couponDiscount,
    couponCode,
    rentalExtraServices,
    totalExtensionsPrice,
    totalExtensionsDays,
    totalUnlimitedFee,
    totalAmountDue,
    remainingDueInstallmentsAmount,
    completedInstallmentsAmount,
    rentToOwnInstallmentBreakdown,
    insuranceName,
    totalWalletPaidAmount,
    addsPrice,
  } = aboutPrice;
  const { locale } = useIntl();
  const totalBeforeInsurance = [
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="1st installment" />,
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 rentToOwnInstallmentBreakdown?.firstPayment
                  }
      
                </span>
      
                </div>
          
          ),
        }
      : {
          msg: <FormattedMessage id="aboutPrice.PricePerDay" />,
          value: 
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                dailyPrice
                  }
      
                </span>
      
                </div>
          )
          
        },
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Monthly installment" />,
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                rentToOwnInstallmentBreakdown?.monthlyInstallment
                  }
      
                </span>
      
                </div>
          
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="Final Installment" />,
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 rentToOwnInstallmentBreakdown?.finalInstallment
                  }
      
                </span>
      
                </div>
          
          ),
        }
      : null,
    bookingType == "rent-to-own"
      ? {
          msg: <FormattedMessage id="No. of months" />,
          value: plan?.noOfMonths,
        }
      : {
          msg: (
            <FormattedMessage
              id="aboutPrice.totalDays"
              values={{
                days: `${numberOfDays}`,
              }}
            />
          ),
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 priceBeforeDiscount
                  }
      
                </span>
      
                </div>
           
          ),
        },
    discountValue
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.discount"
              values={{
                discount: discountType ? `${discountType}` : null,
                percentage: `${discountPercentage}`,
              }}
            />
          ),
          value:
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 discountValue
                  }
      
                </span>
      
                </div>
          )
        }
      : null,
    couponCode
      ? {
          msg: <FormattedMessage id="code.label" />,
          value: couponCode,
        }
      : null,

    couponCode
      ? {
          msg: <FormattedMessage id="couponDiscount" />,
          value:
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 couponDiscount
                  }
      
                </span>
      
                </div>
          )
          
        }
      : null,

    {
      msg: "",
      value: (

        <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
             priceBeforeInsurance
              }
  
            </span>
  
            </div>
       
      ),
    },
  ];
  const extras = [
    insuranceIncluded
      ? {
          msg: (
            <FormattedMessage
              id="aboutPrice.insurance"
              values={{
                insurance: insurance.insuranceName || BookingDetails?.rentalDetails?.insuranceName,
              }}
            />
          ),
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                 insuranceValue || BookingDetails?.rentalDetails?.totalInsurancePrice
                  }
      
                </span>
      
                </div>
           
          ),
        }
      : null,

    rentalExtraServices?.length
      ? rentalExtraServices?.map((service) => ({
          msg: service[`${locale}Title`] || <FormattedMessage id="aboutPrice.extraServices" />,
          value: 
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  service?.totalServiceValue
                  }
      
                </span>
      
                </div>
          )
        }))
      : null,

    isUnlimited
      ? {
          msg: <FormattedMessage id="Unlimited.KM" />,
          value:
            totalUnlimitedFee == 0 ? (
              <FormattedMessage id="free" />
            ) : (

              <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  totalUnlimitedFee
                  }
      
                </span>
      
                </div>
              
            ),
        }
      : null,

    !deliveryPrice
      ? {
          msg: "",
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  totalAddsPrice || addsPrice
                  }
      
                </span>
      
                </div>
          
          ),
        }
      : null,
  ];
  const carDeliveryService = [
    deliveryPrice
      ? {
          msg: <FormattedMessage id="aboutPrice.deliveryCost" />,
          value: 
          
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  deliveryPrice
                  }
      
                </span>
      
                </div>
          )
        }
      : null,

    handoverPrice != 0 && handoverPrice != null
      ? {
          msg: <FormattedMessage id="handoverprice" />,
          value:
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  handoverPrice
                  }
      
                </span>
      
                </div>
          )
        }
      : null,
    {
      msg: "",
      value: (
        <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  totalAddsPrice || addsPrice
                  }
      
                </span>
      
                </div>
        
      ),
    },
  ];

  const vatAndTotal = [
    {
      msg: (
        <>
          <FormattedMessage id="Extensions Total" />
          <span>({totalExtensionsDays})</span>
        </>
      ),
      value: 
      (
        <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
              totalExtensionsPrice
              }
  
            </span>
  
            </div>
      )
      
    },

    valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.total" values={{ vat: valueAddedTaxPercentage }} />,
          value: 
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
              priceBeforeTax
              }
  
            </span>
  
            </div>
          )
          
        }
      : null,

    valueAddedTaxPercentage
      ? {
          msg: <FormattedMessage id="aboutPrice.vat" values={{ vat: valueAddedTaxPercentage }} />,
          value:
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  taxValue
                  }
      
                </span>
      
                </div>
          )
          
        }
      : null,
    {
      msg: (
        <>
          <FormattedMessage id="aboutPrice.grandTotal" /> <FormattedMessage id="aboutPrice.+vat" />
        </>
      ),
      value: (

        <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
              totalPrice
              }
  
            </span>
  
            </div>
       
      ),
    },
    {
      msg: (
        <>
          <FormattedMessage id="Completed.Payments" values={{ vat: valueAddedTaxPercentage }} />
          {` (${BookingDetails?.rentalDetails?.paidInstallmentsCount}) `}
        </>
      ),
      value: (
        <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
              completedInstallmentsAmount
              }
  
            </span>
  
            </div>
       
       
      ),
    },

    // Remaining Due

    totalWalletPaidAmount
      ? {
          msg: (
            <>
              <FormattedMessage id="wallet" />
            </>
          ),
          value: 
          (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
        <RiyalComponent />
            <span>
              {
              totalWalletPaidAmount
              }
  
            </span>
  
            </div>
          )
        }
      : null,

    BookingDetails?.rentalDetails.walletTransactions
      ? {
          msg: (
            <>
              <FormattedMessage id="Due Amount" />
            </>
          ),
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  totalAmountDue
                  }
      
                </span>
      
                </div>
           
          ),
        }
      : null,

    BookingDetails?.rentalDetails?.mergedInstallments &&
    BookingDetails?.rentalDetails?.mergedInstallments.length
      ? {
          msg: (
            <>
              <FormattedMessage id="Remaining.Due" />
            </>
          ),
          value: (
            <div style={{display:"flex",gap:"3px",alignItems:"center",direction:"ltr"}}>
            <RiyalComponent />
                <span>
                  {
                  BookingDetails?.rentalDetails?.remainingDueInstallmentsAmount ||
                  remainingDueInstallmentsAmount
                  }
      
                </span>
      
                </div>
            
          ),
        }
      : null,
    BookingDetails?.rentalDetails?.refundedAmount
      ? {
          msg:
            locale == "en" ? (
              <>
                <span className="text-info">
                  {BookingDetails?.rentalDetails?.refundedAmount} SAR has been refunded to your
                  account
                </span>
              </>
            ) : (
              <span className="text-info">
                تم إيداع قيمة {BookingDetails?.rentalDetails?.refundedAmount} ريال لحساب بطاقتكم
              </span>
            ),
          value: "",
        }
      : null,
  ];
  function DataDisplay(i, row) {
    return (
      <ListItem
        key={JSON.stringify(i)}
        data-testid={`data-info-${i}`}
        className="d-flex justify-content-between align-items-center p-20"
      >
        {row?.msg ? <span>{row?.msg}</span> : <hr />}
        <span>{row?.value}</span>
      </ListItem>
    );
  }
  return BookingPrice || calculatingPrice || !change ? (
    <>
      {calculatingPrice && (
        <CircularProgress variant="determinate" size={40} thickness={4} value={100} />
      )}
      {!inBookingDetails && (
        <h3>
          <FormattedMessage id="aboutPrice" />
        </h3>
      )}

      <div className="alert alert-secondary">
        <h5>
          <FormattedMessage id="aboutPrice.Basic" />
        </h5>

        {totalBeforeInsurance.map((row, i) => row !== null && DataDisplay(i, row))}
        {totalAddsPrice > 0 ||
        BookingDetails?.rentalDetails?.totalAddsPrice ||
        BookingDetails?.rentalDetails?.addsPrice ||
        addsPrice ? (
          <>
            <h5>
              <FormattedMessage id="aboutPrice.extraServices" />
            </h5>
            {extras.length
              ? extras.flat(10)?.map((row, i) => row !== null && DataDisplay(i, row))
              : "null"}
          </>
        ) : null}
        {BookingDetails?.rentalDetails?.deliveryPrice || deliveryPrice ? (
          <>
            <h5>
              <FormattedMessage id="Car_Delivery" />
            </h5>
            {carDeliveryService.map((row, i) => row !== null && DataDisplay(i, row))}
          </>
        ) : null}
        {
          <>
            {/* <h5>
          <FormattedMessage id="aboutPrice.total" /> سس
        </h5> */}
            {vatAndTotal.map((row, i) => row !== null && DataDisplay(i, row))}
          </>
        }
      </div>
    </>
  ) : null;
}

BookingInstallmentPriceSummary.propTypes = {
  BookingPrice: PropTypes.object,
  insurance: PropTypes.object,
  calculatingPrice: PropTypes.bool,
};

export default BookingInstallmentPriceSummary;
