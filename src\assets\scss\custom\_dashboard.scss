/*========== Dashboard Scss ========*/
.dash-cards,
.dash-cards-lg {
  .card-top-action {
    border-radius: 100%;
    color: $white;
    box-shadow: 0 5px 9px 0 rgba(0, 0, 0, 0.21);
    position: absolute;
    top: -10px;
    z-index: 9;
    left: 25px;
  }
  [class*="col-"] {
    padding: 0 0.625rem;
  }
  .recharts-tooltip-wrapper{
    left: 0;
  }
  .card {
    background-color: $block-bg;
    box-shadow: $block-shadow;
    border: $block-border;
    border-radius: $block-border-radius;
    margin: 0.125rem 0.125rem $block-margin-bottom 0.125rem;
    transition: $block-transition;
    padding: $block-spacing;
    position: relative;
    min-height: 190px;
    &:hover {
      box-shadow: $block-hover-shadow;
    }
    .card-title {
      color: $block-title-color;
      margin-bottom: 1.25rem; //20px;
    }
    > span {
      color: $block-title-color;
    }
  }
}
.Crm-wrapper .MuiAppBar-colorPrimary {
  background-color: #F5F5F5 !important;
  color: #B59FF4;

}
.Crm-wrapper .MuiAppBar-colorPrimary .MuiTabs-flexContainer:last-child{
  background-color: #B59FF4 !important; 
  
}
.PrivateTabIndicator-colorSecondary-12{
  background-color: #B59FF4 !important; 
}
.PrivateTabIndicator-colorSecondary-1{
  background-color: #B59FF4 !important; 
}
.PrivateTabIndicator-colorSecondary-2{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-3{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-4{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-5{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-6{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-7{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-8{
  background-color: #B59FF4 !important; 

}
.PrivateTabIndicator-colorSecondary-9{
  background-color: #B59FF4 !important; 

}
.rct-weather-widget {
  min-height: 190px;
  .weather-content {
    text-align: justify;
  }
  .dayTime {
    line-height: 30px;
  }
  i {
    line-height: 110px;
  }
  h2 {
    font-size: 2rem; //36px;
    font-weight: 500;
  }
  .black-overlay {
    border-radius: 5px;
  }
}
.black-overlay {
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.default-btn-group {
  .btn-secondary {
    background: $block-bg;
    color: $gray-900;
    border-color: $gray-300;
    &:hover,
    &.active,
    &:focus {
      background-color: $gray-200 !important;
      color: $gray-900 !important;
      border-color: $gray-300 !important;
    }
  }
}
.ladgend {
  width: 12px;
  display: inline-block;
  height: 12px;
  border-radius: 30px;
  margin-right: 0.625rem; //10px;
  vertical-align: middle;
}
.import-report {
  font-size: 1.375rem; //22px;
  color: $gray-500;
  &:hover {
    color: $blue;
  }
}
.card-action a {
  color: $gray-600;
  font-size: 0.75rem; //12px;
  margin-right: 0.9375rem; //15px;
  &:last-child {
    margin-right: 0;
  }
  i {
    font-size: 0.875rem;
    vertical-align: middle;
    margin-right: 0.625rem; //10px;
  }
}
.card-gradient-primary {
  .card-heading {
    color: $white;
    padding: 3.125rem; //50px;
    line-height: 40px;
  }
}
.top-selling {
  .product-img {
    img {
      width: 275px !important;
      margin: 0 auto;
    }
  }
}
.editor {
  label {
    padding: 0.375rem !important;
  }
  input {
    border: none;
    box-shadow: none;
    &:focus {
      box-shadow: none;
    }
  }
  .form-wrap {
    @include border(1px solid, $border-color, bottom);
  }
}
.counter-point {
  font-size: 1.5rem; //24px;
  color: $gray-600;
}
.featured-section-icon {
  color: $gray-500;
  font-size: 3.75rem; //60px
}
.responsive-div {
  overflow-x: scroll;
}
.current-widget {
  padding: 1.875rem; //30px;
  border-radius: 10px;
  color: $white;
  margin-bottom: $block-margin-bottom;
  box-shadow: $block-shadow;
  transition: $block-transition;
  &:hover {
    box-shadow: $block-hover-shadow;
  }
  h3 {
    font-size: 1.875rem; //30px
  }
  h2 {
    font-size: 2.6rem; //50px;
    font-weight: 500;
  }
  i {
    font-size: 3.125rem; //50px;
  }
}
.speedometer {
  width: 100% !important;
}

.pt-xl {
  padding-top: 8rem;
}
/*======== Responsive =======*/
@media (max-width: 1669px) and (min-width: 320px) {
  .dash-cards {
    .media {
      .mr-25 {
        margin-right: 0.625rem; //10px !important;
      }
      img {
        width: 50px;
        height: 50px;
      }
    }
  }
}
@media (max-width: 1425px) and (min-width: 941px) {
  .rct-weather-widget {
    .weather-content {
      text-align: center;
    }
    h2,
    h3,
    h4 {
      font-size: 1rem;
      margin: 0;
    }
    i {
      line-height: 70px;
      font-size: 2.5rem !important; //40px;
    }
  }
}
@media (max-width: 1560px) and (min-width: 320px) {
  .hover-action button,
  .hover-action a {
    min-height: 28px;
    max-width: 28px;
    height: 28px !important;
    font-size: 1rem !important;
  }
  .weather-top img {
    height: 220px;
  }
}
@media (max-width: 1460px) and (min-width: 320px) {
  .product-img {
    img {
      width: 300px !important;
      margin: 0 auto;
    }
  }
  .current-widget {
    h2 {
      font-size: 1.6rem;
    }
  }
}
@media (max-width: 1430px) {
  .rct-header > div {
    padding: 0 0.625rem;
  }
}
@media (max-width: 1400px) {
  .counter-point ~ p {
    display: none;
  }
}
@media (max-width: 1366px) and (min-width: 992px) {
  .current-widget {
    padding: 0.9375rem; //15px;
    i {
      font-size: 2.125rem;
    }
    h2,
    h3 {
      font-size: 1.8rem;
    }
    h3 {
      margin-bottom: 0.625rem !important; //10px;
    }
  }
}
@media (max-width: 1300px) and (min-width: 992px) {
  .card-heading {
    padding: 1.125rem !important;
  }
  .rct-weather-widget {
    .d-flex {
      display: inline-block !important;
    }
  }
}
@media (max-width: 1269px) {
  .new-customer-list li button {
    font-size: 1rem !important;
    height: 20px !important;
    width: 20px !important;
    line-height: 20px !important;
  }
  .featured-section-icon {
    font-size: 2.75rem;
  }
  .counter-point {
    font-size: 1.2rem;
  }
}
@media (max-width: 1199px) and (min-width: 320px) {
  .fixed-plugin {
    .app-settings {
      > li {
        &:nth-of-type(2),
        &:nth-of-type(3) {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 900px) and (min-width: 320px) {
  .navbar-right {
    li {
      a button,
      button {
        width: 30px;
        height: 30px;
        .badge-top-right {
          top: -6px;
          right: 0;
        }
      }
    }
  }
  .rct-dropdown,
  .notification-icon,
  .cart-icon {
    &.show .dropdown-menu {
      transform: translate3d(0, 40px, 0) !important;
    }
  }
  .notification-icon,
  .cart-icon {
    .dropdown-menu {
      right: -32px !important;
    }
  }
  .rct-footer.d-flex {
    display: block !important;
    text-align: center;
    padding: 0.625rem; //10px;
    .footer-menus {
      margin-bottom: 0.625rem !important;
      li {
        a {
          padding: 5px !important;
          min-width: 20px;
          min-height: 30px;
        }
      }
    }
  }
}
@media (max-width: 900px) and (min-width: 576px) {
  .w-xs-full {
    max-width: 100% !important;
    flex-basis: 100% !important;
  }
  .w-xs-half-block {
    max-width: 50% !important;
    flex-basis: 50% !important;
  }
}
@media (max-width: 835px) {
  .navbar-left > li:nth-of-type(2),
  .navbar-right > li:last-child {
    display: none;
  }
}
@media (max-width: 800px) and (min-width: 320px) {
  .dropdown-group-link {
    img {
      margin-right: 0 !important;
    }
    span {
      display: none;
    }
  }
}
@media (max-width: 800px) and (min-width: 576px) {
  .w-8-full {
    max-width: 100% !important;
    flex-basis: 100% !important;
  }
  .w-8-half-block {
    max-width: 50% !important;
    flex-basis: 50% !important;
  }
}
@media (max-width: 767px) and (min-width: 320px) {
  .rct-page-content {
    padding: 1.25rem;
  }
}
@media (max-width: 650px) {
  .search-icon {
    width: auto;
    &::before {
      left: 5px;
      top: -25px;
      font-size: 2rem;
      cursor: pointer;
    }
    > div {
      display: none;
    }
  }
}
@media (max-width: 599px) {
  .to-do-list {
    label {
      margin: 0;
      width: 30px;
      > span {
        width: 30px;
        height: 30px;
      }
    }
  }
  .new-customer-list {
    .media {
      .mr-15 {
        margin-right: 5px !important;
      }
    }
  }
  .dashboard-overlay {
    top: 57px;
  }
}
@media (max-width: 575px) {
  .page-title-wrap,
  .breadcrumb {
    display: inline-block;
  }
  .breadcrumb {
    // float: right;
    padding: 0 0.625rem;
  }
  .page-title {
    h2 {
      font-size: 1.2rem;
    }
    .page-title-wrap {
      i {
        vertical-align: -1px;
        margin-right: 5px;
      }
    }
    .breadcrumb-item + .breadcrumb-item::before {
      padding: 0 0.3rem;
    }
    .rct-creadcrumb {
      font-size: 11px;
    }
  }
  .all-mails-list {
    .w-90 {
      width: 100% !important;
    }
  }
}
@media (max-width: 472px) {
  .navbar-right {
    > li {
      &:nth-of-type(2) {
        display: none;
      }
      .dropdown-group-link {
        padding: 5px;
        &.dropdown-toggle::after {
          display: none;
        }
      }
    }
  }
  .chat-box-main img {
    width: 80px;
  }
  .page-title {
    h2 {
      // width: 50px;
      overflow: hidden;
      // text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: middle;
    }
  }
}
.custom-popup {
  background-color: white;
}
div[aria-labelledby="modal-modal-title"] {
  display: flex;
  justify-content: center;
  align-items: center;
}

.MuiOutlinedInput-input{
  padding: 10.5px 14px !important;
}
label{
  padding-right: 5px !important
}
.alert-info hr{
  width: 84% !important;
  border-width: 4px;
}
:dir(rtl) .recharts-layer{
  direction: ltr;
}
p{
  margin: 0;
}
// .css-g1d714-ValueContainer{
//   max-height: 100px;
//   overflow-y: auto !important;
// }
// .css-yk16xz-control{
//   max-height: 100px;
//   overflow-y: auto !important;
// }
.dropdown-select > div:first-of-type{
  max-height: 100px ;
  overflow-y: auto !important;
}
.modal-content{
  width: 97% !important;
  margin: auto !important;
  @media (max-width: 960px){
    width: 97vw !important;
    margin: 0 !important;
  }
}
.modal-title{
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/* Modern elegant card styling for InfoCard components */
.list_item_info {
  transition: all 0.3s ease-in-out;
  border-radius: 12px;
  margin: 8px 0;
  padding: 15px !important;
  background: #ffffff;
  border: 1px solid #e8ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  
  &:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:nth-of-type(odd) {
    background: #ffffff;
  }

  &:nth-of-type(even) {
    background: #fafbfc;
    border-color: #f1f3f4;
  }

  /* Enhanced text styling */
  .text-align-localized {
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
  }

  /* Value styling */
  > span:last-child {
    font-weight: 600;
    color: #212529;
    font-size: 0.95em;
  }

  /* Add spacing between content elements */
  > * + * {
    margin-top: 8px;
  }
}

/* Card container improvements */
.rct-collapsible-card {
  .card-body {
    padding: 0;
  }
  
  .list {
    margin: 0;
    padding: 12px;
  }
}

/* Modern card header styling */
.card-header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  
  h4, h5 {
    margin: 0;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

/* Enhanced spacing and typography */
.info-card-container {
  margin-bottom: 28px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  background: #ffffff;
  
  &:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
}

/* Elegant button styling - Enhanced for Material-UI */
.btn:not(.react-datepicker__navigation):not(.react-datepicker__day):not(.react-datepicker__month-text):not(.react-datepicker__quarter-text):not(.react-datepicker__year-text), 
.MuiButton-root:not([class*="MuiPickers"]):not([class*="MuiDatePicker"]):not([class*="MuiTimePicker"]):not([class*="MuiDateTimePicker"]):not(.MuiPickersDay-root):not(.MuiPickersCalendarHeader-iconButton):not(.MuiPickersArrowSwitcher-button):not(.MuiPickersToolbar-penIconButton):not(.MuiPickersClockPointer-root):not(.MuiPickersClockNumber-root) {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 0 4px !important;
  transition: all 0.2s ease !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  text-transform: none !important;
  
  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }
}

/* Primary button - Modern blue gradient */
.btn-primary, .MuiButton-containedPrimary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  }
}

/* Secondary button - Elegant purple */
.btn-secondary, .MuiButton-containedSecondary {
  background: linear-gradient(135deg, #a855f7 0%, #c084fc 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%) !important;
  }
}

/* Success button - Fresh green */
.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
  }
}

/* Warning button - Warm orange */
.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%) !important;
  }
}

/* Danger button - Modern red */
.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
  }
}

/* Info button - Cool cyan */
.btn-info {
  background: linear-gradient(135deg, #06b6d4 0%, #67e8f9 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%) !important;
  }
}

/* Default button - Elegant gray */
.btn-default, .MuiButton-root[color="default"] {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%) !important;
  }
}

/* Timeline button - Warm brown gradient */
.btn-timeline {
  background: linear-gradient(135deg, #8b5a2b 0%, #d2691e 100%) !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #7a4a1f 0%, #b8541a 100%) !important;
  }
}

/* Assign button - Distinctive purple-pink gradient */
.btn-assign {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3) !important;
  
  &:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4) !important;
    transform: translateY(-1px) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3) !important;
  }
}

/* Button group spacing */
.btn-group {
  gap: 8px;
  
  .btn {
    margin: 0;
  }
}

/* Action buttons container spacing */
.d-flex {
  &.gap-2 {
    gap: 8px;
  }
  
  &.flex-wrap {
    gap: 8px 12px;
  }
}

/* Page title bar button improvements */
.page-title-bar {
  .extra-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
  }
}

/* Enhanced header alignment */
.page-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  
  .page-title {
    flex: 1;
    margin: 0;
  }
  
  .extra-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-left: auto;
  }
}

/* Fix for Material-UI button container */
.d-flex.flex-wrap.gap-2 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;
  
  .MuiButton-root {
    min-width: auto;
    white-space: nowrap;
  }
  
  .MuiTooltip-root {
    display: inline-flex;
  }
}

/* Booking Details Page Card Consistency */
.ecom-dashboard-wrapper {
  .row {
    margin-left: -15px;
    margin-right: -15px;
    
    .col-md-6, .col-12 {
      padding-left: 15px;
      padding-right: 15px;
    }
  }
  
  /* New booking details card styling */
  .booking-details-card {
    margin-bottom: 25px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .rct-collapsible-card {
      margin-bottom: 0;
      
      .card {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
          transform: translateY(-2px);
        }
      }
      
      .card-header {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
        color: white;
        border-radius: 12px 12px 0 0 !important;
        padding: 20px 24px;
        
        h4, h5, p {
          margin: 0;
          font-weight: 600;
          color: white;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
      
      .card-body {
        padding: 20px;
      }
    }
  }
  
  /* Ensure consistent spacing between cards */
  .rct-block {
    margin-bottom: 25px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  /* Consistent card title styling */
  .rct-block-title {
    h4 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #2c2c2c;
      margin-bottom: 0;
      line-height: 1.4;
    }
    
    p {
      font-size: 1.125rem;
      font-weight: 600;
      color: #2c2c2c;
      margin-bottom: 0;
      line-height: 1.4;
    }
  }
  
  /* Card content padding consistency */
  .rct-block-content {
    padding: 20px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .row {
      margin-left: -10px;
      margin-right: -10px;
      
      .col-md-6, .col-12 {
        padding-left: 10px;
        padding-right: 10px;
      }
    }
    
    .booking-details-card {
      // margin-bottom: 20px;
    }
    
    .rct-block {
      margin-bottom: 20px;
    }
    
    .rct-block-content {
      padding: 15px;
    }
  }
}

/* Remove the problematic p-0 override */
.ecom-dashboard-wrapper .col-md-6.p-0,
.ecom-dashboard-wrapper .col-12.p-0 {
  padding-left: 15px !important;
  padding-right: 15px !important;
  
  @media (max-width: 768px) {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}