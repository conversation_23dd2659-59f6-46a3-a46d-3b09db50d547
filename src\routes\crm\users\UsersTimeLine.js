import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dalHeader, <PERSON>dalBody, ModalFooter } from "reactstrap";
import { useQuery } from "@apollo/client";
import { CustomerAudits } from "gql/queries/CustomerTimeLine.gql"
import moment from "moment";
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import "./style.css";
// import { JSONParser } from "@amcharts/amcharts4/core";
const UsersTimeLine = (props) => {
  const { locale } = useIntl();

  const { className } = props;
  const [oldData, setOldData] = useState();
  const [newData, setNewData] = useState();
  const { data: customerAudit, refetch } = useQuery(CustomerAudits, {
    skip: !props.CustomerId,
    variables: { id:props?.CustomerId},
  });
  useEffect(() => {
    if (customerAudit?.customerAudits?.length) {
      const newData = [];
      const oldData = [];
      customerAudit?.customerAudits?.map((rental, i) => {
        newData.push(rental?.newData);
        oldData.push(rental?.oldData);
      });
      setOldData(oldData);
      setNewData(newData);
    }
  }, [customerAudit]);
  useEffect(() => {
    if (!props.CustomerId) {
      return;
    }
    refetch();
  }, [props.isOpen]);
  const toggle = () => props.setOpenTimeLineModal(!props.isOpen);

  return (
    <Modal isOpen={props.isOpen} toggle={toggle} className={className}>
      <ModalHeader toggle={toggle}>
        <FormattedMessage id="UsersTimeLine" />
      </ModalHeader>
      <ModalBody>
        {customerAudit?.customerAudits?.length ? (
          <div class="container" style={{ height: "500px", overflowY: "auto" }}>
            <div class="row">
              <div class="col-md-10">
                <ul class="cbp_tmtimeline" style={{direction: locale =="ar" ? "ltr" : ""}}>
                  <li style={{width:"90%"}}>
                    <div class="cbp_tmicon">
                      <i class="zmdi zmdi-account"></i>
                    </div>
                    <div class="cbp_tmlabel empty">
                      {" "}
                      <span style={{ fontWeight: "bold" }}>
                        <FormattedMessage id="userId" /> :
                        {props.CustomerId}
                      </span>{" "}
                    </div>
                  </li>

                  {customerAudit?.customerAudits.map((rental, index) => (
                    <li style={{width:"90%",wordWrap:"break-word"}}>

                      <time
                        class="cbp_tmtime"
                        style={{ left: locale == "ar" ? "1px" : "" }}
                        datetime=""
                      >
                        <span>{rental.userName}</span>{" "}
                        <span style={{ direction: "ltr" }}>
                          {moment.utc(rental.createdAt).locale(locale).format("DD/MM/YYYY h:mm:ss a")}
                        </span>

                      </time>
                      <div class="cbp_tmicon bg-info">
                        <i class="zmdi zmdi-label"></i>
                      </div>
                       <div className="cbp_tmlabel">
                      
                      <div class=" d-flex" style={{ justifyContent: "space-between" }}>
                          
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="oldData" />
                          </h2>
                          <ul>
                          
                            {oldData &&
                              oldData[index] &&
                              Object.entries(oldData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? `${key}` : "0"} />}
                                    {": "}
                                    {
                                    val != null &&
                                    (key == "for_new_customers" || key == "is_active")
                                  
                                      ?                                       
                                     val ? 
                                     <CheckIcon style={{color:"green",verticalAlign:"middle"}}/>
                                     :  
                                  
                                     <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                    
                                    
                                      :
                                      val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{"  "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                        <div className="w-50" style={{direction:locale =="ar" ? "rtl" : "ltr"}}>
                          <h2 style={{ fontWeight: "bold" }}>
                            <FormattedMessage id="newData" />
                          </h2>
                          <ul>
                            {newData &&
                              newData[index] &&
                              Object.entries(newData[index]).map(([key, val]) => (
                                <>
                                  <li style={{width:"90%"}}>
                                    {<FormattedMessage id={key ? key : "0"} />} :{" "}
                                    {
                                    val != null &&
                                    (key == "for_new_customers" || key == "is_active")
                                      
                                      ?
                                      val  ?
                                        <CheckIcon style={{color:"green",verticalAlign:"middle"}} />
                                      :  
                                   
                                      <CloseIcon style={{color:"red",verticalAlign:"middle"}} />
                                      
                                    
                                      :
                                       val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />}{" "}
                                  </li>
                                </>
                              ))}
                          </ul>
                        </div>
                      </div>
                      </div>

                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ) : (
          <div className="d-flex" style={{ justifyContent: "center" }}>
            <FormattedMessage id="No data found" />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>
          <FormattedMessage id="close"/>
        </Button>
      </ModalFooter>
    </Modal>
  );
};
export default UsersTimeLine;
