/* eslint-disable prettier/prettier */
import React from "react";
import { dataTypes } from "constants/constants";
import { Link } from "react-router-dom";
import moment from "moment";
import { FormattedMessage } from "react-intl";
import { userCan } from "functions";

const { TEXT, ACTIONS, FUNC } = dataTypes;
export const Data = [
  {
    headerId: "agency.id",
    dataRef: "id",
    dataType: FUNC,
    func: (record) =>  userCan("agencies.view") ? <Link to={`/cw/dashboard/agencies/${record.id}`}>{record.id}</Link> :record.id,
  },
  {
    headerId: "agency.name",
    dataType: FUNC,
    func: (record, locale) => (
      userCan("agencies.view") ? 
      <Link to={`/cw/dashboard/agencies/${record.id}`}>
        {record[`name${locale.charAt(0).toUpperCase() + locale.slice(1)}`]}
      </Link>
        :record[`name${locale.charAt(0).toUpperCase() + locale.slice(1)}`]
    ),
  },
  {
    headerId: "agency.logo",
    dataRef: "logo",
    dataType: FUNC,
    func: (record) => (record?.logo ? <img src={record.logo} alt="Logo" width="50px" /> : <></>),
  },

  {
    headerId: "phoneNumber",
    dataRef: "phoneNumber",
    dataType: TEXT,
  },
  {
    headerId: "email",
    dataRef: "email",
    dataType: TEXT,
  },
  {
    headerId: "status",
    dataType: FUNC,
    func: (record, locale, refetch, formatMessage) =>
      record.isActive ? formatMessage({ id: "Active" }) : formatMessage({ id: "inactive" }),
  },
  {
    headerId: "createdAt",
    dataRef: "createdAt",
    dataType: FUNC,
    func: (record, locale) => {
      if (record?.createdAt) {
        const date = moment(record.createdAt).locale(locale);
        return (
          <div style={{ display: "flex", gap: "5px" }}>
            <span style={{ fontSize: "small" }}>{date.format("LL")}</span> <br />{" "}
            <span>{date.format("hh:mm A")}</span>
          </div>
        );
      }
      return "";
    },
  },

  { headerId: "common.actions", dataType: ACTIONS },
];
