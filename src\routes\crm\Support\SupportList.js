/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/** Bookings List */
import React, { useEffect, useState } from "react";
import { useIntl, FormattedMessage } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typography, Tooltip, Switch } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import { useMutation } from "@apollo/client";
import { userCan } from "functions/userCan";
import swal from "sweetalert";
import TotalResults from "components/shared/TotalResults";
import { SupportData } from "./SupportData";
import TicketStatusModel from "./TicketStatusModel";

function SupportList({ allfeedbacks, loading, setPage, limit, setLimit, refetch }) {
  const [isOpen, setIsopen] = useState();
  const [record, setRecord] = useState();
  const history = useHistory();
  const { formatMessage } = useIntl();
  const [feedbacks, setFeedbacks] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = feedbacks;

  useEffect(() => {
    setFeedbacks({
      collection: allfeedbacks?.feedbacks?.collection,
      metadata: allfeedbacks?.feedbacks?.metadata,
    });
  }, [allfeedbacks]);

  const handelManageTicket = (record) => {
    setRecord(record);
    setIsopen(true);
  };

  const actions = ({ id, status }) => (
    <div className="d-flex align-items-center" style={{ gap: "5px" }}>
      {userCan("feedbacks.manage") && (
        <button className="btn btn-primary" onClick={() => handelManageTicket({ id, status })}>
          <FormattedMessage id="manage.ticket" />
        </button>
      )}
    </div>
  );
  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={SupportData}
            loading={loading}
            tableRecords={collection}
            actions={actions}
            actionsArgs={["id", "status"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              specialPagination={[10, 20, 40, 80, 100]}
              handlePerPageChange={(value) => {
                setLimit(value);
              }}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>
      {isOpen && record && (
        <TicketStatusModel setIsopen={setIsopen} isOpen={isOpen} record={record} />
      )}
    </Typography>
  );
}

SupportList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  refetch: PropTypes.func,
  loading: PropTypes.bool,
  allfeedbacks: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default SupportList;
