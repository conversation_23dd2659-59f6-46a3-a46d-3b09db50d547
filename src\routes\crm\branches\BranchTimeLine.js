import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dalHeader, <PERSON>dalBody, ModalFooter } from "reactstrap";
import { useQuery } from "@apollo/client";
import { BranchAudits } from "gql/queries/BranchAudits.gql";
import moment from "moment";
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import "./style.css";
// import { JSONParser } from "@amcharts/amcharts4/core";
const BranchTimeLine = (props) => {
  const { locale } = useIntl();

  const { className } = props;
  const [oldData, setOldData] = useState();
  const [newData, setNewData] = useState();
  const { data: branchAudits, refetch } = useQuery(BranchAudits, {
    skip: !props.BranchId,
    variables: { id: props?.BranchId },
  });
  useEffect(() => {
    if (branchAudits?.branchAudits?.length) {
      const newData = [];
      const oldData = [];
      branchAudits?.branchAudits?.map((item, i) => {
        newData.push(item?.newData);
        oldData.push(item?.oldData);
      });
      setOldData(oldData);
      setNewData(newData);
    }
  }, [branchAudits]);
  useEffect(() => {
    if (!props.BranchId) {
      return;
    }
    refetch();
  }, [props.isOpen]);
  const toggle = () => props.setOpenTimeLineModal(!props.isOpen);

  return (
    <Modal isOpen={props.isOpen} toggle={toggle} className={className}>
      <ModalHeader toggle={toggle}>
        <FormattedMessage id="BranchTimeLine" />
      </ModalHeader>
      <ModalBody>
        {branchAudits?.branchAudits?.length ? (
          <div class="container" style={{ height: "500px", overflowY: "auto" }}>
            <div class="row">
              <div class="col-md-10">
                <ul class="cbp_tmtimeline" style={{ direction: locale == "ar" ? "ltr" : "" }}>
                  <li style={{ width: "90%" }}>
                    <div class="cbp_tmicon">
                      <i class="zmdi zmdi-account"></i>
                    </div>
                    <div class="cbp_tmlabel empty">
                      {" "}
                      <span style={{ fontWeight: "bold" }}>
                        <FormattedMessage id="BranchID" /> :{props.BranchId}
                      </span>{" "}
                    </div>
                  </li>

                  {branchAudits?.branchAudits.map((rental, index) => (
                    <li style={{ width: "90%" }}>
                      <time
                        class="cbp_tmtime"
                        style={{ left: locale == "ar" ? "1px" : "" }}
                        datetime=""
                      >
                        <span>{rental.userName}</span>{" "}
                        <span style={{ direction: "ltr" }}>
                          {moment.utc(rental.createdAt).local().format("DD/MM/YYYY h:mm:ss a")}
                        </span>
                        {/* <span>{JSON.parse(  ).map((role)=>role)}</span> */}
                      </time>
                      <div class="cbp_tmicon bg-info">
                        <i class="zmdi zmdi-label"></i>
                      </div>
                      <div className="cbp_tmlabel">
                        {rental?.referenceNo ? (
                          <div style={{ direction: "rtl" }}>
                            <span>
                              <FormattedMessage id="Extension.id" /> :{rental?.referenceNo}
                            </span>
                          </div>
                        ) : null}

                        <div class=" d-flex" style={{ justifyContent: "space-between" }}>
                          <div
                            className="w-50"
                            style={{ direction: locale == "ar" ? "rtl" : "ltr" }}
                          >
                            <h2 style={{ fontWeight: "bold" }}>
                              <FormattedMessage id="oldData" />
                            </h2>
                            <ul>
                              {oldData &&
                                oldData[index] &&
                                Object.entries(oldData[index]).map(([key, val]) => (
                                  <>
                                    <li style={{ width: "90%" }}>
                                      {<FormattedMessage id={key ? `${key}` : "0"} />}
                                      {": "}
                                      {
                                      val === true ?
                                      <CheckIcon />
                                      : val === false ?
                                      <CloseIcon />
                                      :                                      
                               val &&
                                      val != null &&
                                      (key === "pick_up_time" ||
                                        key === "drop_off_time" ||
                                        key == "refunded_at") ? (
                                        moment(oldData[index][key], "HHmmss").format("HH:mm:ss a")
                                      ) : key == "decline_reason" ? (
                                        <>
                                          <ul style={{ paddingInline: "10px" }}>
                                            {val?.split(",").map((value) => (
                                              <li> {value}</li>
                                            ))}
                                          </ul>
                                        </>
                                      ) : (
                                        val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />
                                      )}
                                      {"  "}
                                    </li>
                                  </>
                                ))}
                            </ul>
                          </div>
                          <div
                            className="w-50"
                            style={{ direction: locale == "ar" ? "rtl" : "ltr" }}
                          >
                            <h2 style={{ fontWeight: "bold" }}>
                              <FormattedMessage id="newData" />
                            </h2>
                            <ul>
                              {newData &&
                                newData[index] &&
                                Object.entries(newData[index]).map(([key, val]) => (
                                  <>
                                    <li style={{ width: "90%" }}>
                                      {<FormattedMessage id={key ? key : "0"} />} :{" "}
                                      {
                                        val === true ?
                                        <CheckIcon />
                                        : val === false ?
                                        <CloseIcon />
                                        :                      
                                      
                                      val &&
                                      val != null &&
                                      (key === "pick_up_time" ||
                                        key === "drop_off_time" ||
                                        key === "refunded_at") ? (
                                        moment(newData[index][key], "HHmmss").format("HH:mm:ss a")
                                      ) : key == "decline_reason" ? (
                                        <>
                                          <ul style={{ paddingInline: "10px" }}>
                                            {val?.split(",").map((value) => (
                                              <li> {value}</li>
                                            ))}
                                          </ul>
                                        </>
                                      ) : (
                                        val != null &&
                                        (val?.length || val) && <FormattedMessage id={val} />
                                      )}{" "}
                                    </li>
                                  </>
                                ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ) : (
          <div className="d-flex" style={{ justifyContent: "center" }}>
            <FormattedMessage id="No data found" />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>
          close
        </Button>
      </ModalFooter>
    </Modal>
  );
};
export default BranchTimeLine;
