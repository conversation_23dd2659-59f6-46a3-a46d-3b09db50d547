/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
/**
 * Bookings List
 */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Link, useHistory, useParams } from "react-router-dom";
import PropTypes from "prop-types";
import { Typography, Tooltip, Switch } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import useSetState from "hooks/useSetState";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import PerPage from "components/shared/PerPage";
import CustomTable from "components/shared/CustomTable";
import { userCan } from "functions/userCan";
import { DeleteCustomer } from "gql/mutations/DeleteCustomer.gql";
import { useMutation } from "@apollo/client";
import swal from "sweetalert";
import { NotificationManager } from "react-notifications";
import { ActivateAgencyCustomer } from "gql/mutations/ActivateAgencyCustomer.gql";
import YakeenVerification from "routes/components/Yakeen";
import TotalResults from "components/shared/TotalResults";
import CustomerTimeLine from "../CustomerTimeLine";
import { customersTableData, agencyCustomersTableData } from "./CustomersListTableData";
function CustomersList({
  customersRes,
  loading,
  setPage,
  refetch,
  limit,
  setLimit,
  isAgency,
  agencyId,
  agency,
}) {
  const history = useHistory();
  const { agencyId: agencyIdParam } = useParams();
  const { formatMessage } = useIntl();
  const [openTimelineModal, setOpenTimeLineModal] = useState(false);
  const [customerId, setCustomerId] = useState();
  const [bookingsState, setBookingsState] = useSetState({
    collection: [],
    metadata: {},
  });
  const { collection, metadata } = bookingsState;
  const [deleteCustomer] = useMutation(DeleteCustomer);
  const [activateAgencyCustomer] = useMutation(ActivateAgencyCustomer);
  useEffect(() => {
    setBookingsState({
      collection: isAgency
        ? customersRes?.agencyCustomers?.collection
        : customersRes?.users?.collection,
      metadata: isAgency ? customersRes?.agencyCustomers?.metadata : customersRes?.users?.metadata,
    });
  }, [customersRes, isAgency]);
  const getCustomerAudit = (id) => {
    setCustomerId(id);
    setOpenTimeLineModal(true);
  };
  const actions = ({ id, customerProfile, isActive, user }) => (
    <>
      {/* Redirects to booking/rental edit */}
      {(userCan("users.update") || (isAgency && agency?.isActive)) && (
        <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
          <Link to={`customers/${isAgency ? user?.id : id}/edit`}>
            <i className=" ti-pencil-alt m-1"></i>
          </Link>
        </Tooltip>
      )}
      {agency?.isActive && !customerProfile?.isDeleted && !isAgency && (
        <Tooltip title={formatMessage({ id: "common.delete" })} placement="top">
          <i
            style={{ cursor: "pointer" }}
            className=" ti-trash"
            onClick={() => handelDeleteCustomer(id)}
          ></i>
        </Tooltip>
      )}
      {((isAgency && agency?.isActive) || agencyIdParam) && userCan("agencies.activation") && (
        <Switch
          checked={isActive}
          color="primary"
          onChange={(e) => {
            if (e.target.checked) {
              swal({
                title: formatMessage({ id: "are.u.sure.?" }),
                text: formatMessage({ id: "you want to activate this user" }),
                icon: "warning",
                buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
                dangerMode: true,
              }).then((result) => {
                if (result) {
                  return activateAgencyCustomer({
                    variables: {
                      input: {
                        agencyId: +agencyIdParam || agencyId,
                        id: user?.id || id,
                        isActive: true,
                      },
                    },
                  })
                    .then(() => {
                      refetch();
                      NotificationManager.success(<FormattedMessage id="Activated Successfully" />);
                    })
                    .catch((error) => {
                      NotificationManager.error(error.message);
                    });
                }
              });
            } else {
              swal({
                title: formatMessage({ id: "are.u.sure.?" }),
                text: formatMessage({ id: "you want to deactivate this user" }),
                icon: "warning",
                buttons: [formatMessage({ id: "no" }), formatMessage({ id: "yes" })],
                dangerMode: true,
              }).then((willDelete) => {
                if (willDelete) {
                  return activateAgencyCustomer({
                    variables: {
                      input: {
                        agencyId: +agencyIdParam || agencyId,
                        id: user?.id || id,
                        isActive: false,
                      },
                    },
                  })
                    .then(() => {
                      refetch();
                      NotificationManager.success(
                        <FormattedMessage id="Deactivated successfully" />,
                      );
                    })
                    .catch((error) => {
                      NotificationManager.error(error.message);
                    });
                }
              });
            }
          }}
          inputProps={{ "aria-label": "primary checkbox" }}
        />
      )}

      <Tooltip title={formatMessage({ id: "common.timeline" })} placement="top">
        <Link>
          <i className="fas fa-history" onClick={() => getCustomerAudit(id)}></i>
        </Link>
      </Tooltip>
    </>
  );
  const handelDeleteCustomer = (id) => {
    swal({
      title: formatMessage({ id: "are.u.sure.?" }),
      text: formatMessage({ id: "u.want.delete.customer" }),
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "delete" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        deleteCustomer({
          variables: {
            input: { userId: +id },
          },
        })
          .then(() => refetch())
          .then((res) =>
            swal(formatMessage({ id: "customerdeletedsuccessfully" }), {
              icon: "success",
            }),
          )
          .catch((error) => {
            swal({
              text: formatMessage({
                id: "Can't delete this customer, as he has an active rental!",
              }),
              icon: "error",
              buttons: {
                text: formatMessage({ id: "ok" }),
              },
              dangerMode: true,
            });
          });
      }
    });
  };
  return (
    <Typography component="div" style={{ padding: 10 }}>
      <div>
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={isAgency && !agencyIdParam ? agencyCustomersTableData : customersTableData}
            loading={loading}
            tableRecords={collection}
            refetch={refetch}
            actions={actions}
            actionsArgs={["id", "allyCompanyId", "userId", "customerProfile", "isActive", "user"]}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}` });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => {
                setLimit(value);
              }}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>
      <CustomerTimeLine
        CustomerId={customerId}
        isOpen={openTimelineModal}
        setOpenTimeLineModal={setOpenTimeLineModal}
      />
    </Typography>
  );
}

CustomersList.propTypes = {
  setPage: PropTypes.func,
  loading: PropTypes.bool,
  customersRes: PropTypes.object,
  setLimit: PropTypes.func,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default CustomersList;
