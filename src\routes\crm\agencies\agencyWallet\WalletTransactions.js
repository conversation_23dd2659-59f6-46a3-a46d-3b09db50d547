/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";
import { useQuery } from "@apollo/client";
import { AgencyTransactions, UserWallet } from "gql/queries/Agencies.gql";
import WalletImg from "assets/wallet.svg";
import { useIntl } from "react-intl";
import styled from "styled-components";
import ChargeBalanceModal from "./chargeBalanceModal";
import { useParams } from "react-router-dom";
import moment from "moment";
import RiyalComponent from "components/shared/RiyalComponent";

const Styled = styled.div`
  .wallet__card {
    margin-top: 30px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    border-radius: 15px;
    padding-bottom: 18px;
    h3 {
      font-weight: bold;
    }
    img {
      margin-top: -18px;
      background: white;
      border-radius: 15px;
      padding: 3px;
    }
  }
  h5,
  p {
    color: #2a292f;
    opacity: 0.5;
    margin-top: 15px !important;
    /* text-align: center !important; */
  }

  hr {
    background: #c4c4c4;
    opacity: 0.5;
  }
`;

const TransactionStyled = styled.div`
  border: solid 2px #d0cece;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: white;
  padding: 0 0 10px 0;
  border-radius: 12px;
  margin-top: 20px;
  margin-bottom: 20px;
  h5 {
    font-weight: bold;
    color: black !important;
    margin-top: 5px !important;
    margin-bottom: 5px !important;
    opacity: 1 !important;
  }

  .date {
    opacity: 0.5;
  }
  img {
    margin: 10px 0;
  }
  > div:last-child {
    text-align: center;
  }
  > div {
    display: flex;
    justify-content: space-between;
    padding: 10px 24px 10px 24px !important;
    border-radius: 10px;
    &.grid {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
    }
  }
  .amount {
    background: #fa9c3f;
    color: white;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
  }
  .added-balance {
    color: #7ab3c5;
    background: #e5f4f8;
    text-align: center;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
    width: max-content;
  }
  .used-balance {
    color: #f65656;
    background: #fee6e6;
    text-align: center;
    border-radius: 6px;
    padding: 2px 6px 6px 6px;
    width: max-content;
  }
  .wrap {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
    @media (max-width: 768px) {
      gap: 0px !important;
    }
  }
`;

function Transaction({ data, formatMessage, language }) {
  if (data) {
    return (
      <TransactionStyled className=" text-align-localized">
        <div className="grid">
          <div className="wrap">
            {data?.bookingNo ? (
              <h5>
                {formatMessage({ id: "Booking No." })} : {data.bookingNo}
              </h5>
            ) : null}
            <h5>
              {formatMessage({ id: "Operation no." })} : {data.transactionNo}
            </h5>
            {data.transactionDate ? (
              <div className="date">
                {moment(data.transactionDate).locale(language).format("ll")}
              </div>
            ) : null}
          </div>
          <div>
            <img src={data.walletSource.img} alt="wallet source icon" />
          </div>
        </div>
        <div>
          <div className={data.isIncoming ? "added-balance" : "used-balance"}>
            {data.isIncoming
              ? formatMessage({ id: "Added Balance" })
              : formatMessage({ id: "Used Balance" })}
          </div>
          <div className="amount">
            {data.isIncoming ? "+" : "-"} {Number(data.amount || 0).toLocaleString()}{" "}
            {formatMessage({ id: "SAR" })}
          </div>
        </div>
      </TransactionStyled>
    );
  }
}
function WalletTransactions() {
  const user_data = JSON.parse(localStorage.getItem("user_data"));
  const isAgency = user_data?.agency_id;
  const { agencyId: agency_id_param } = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { locale, formatMessage } = useIntl();
  const { data, loading, refetch } = useQuery(isAgency ? AgencyTransactions : UserWallet, {
    variables: {
      limit: 1000,
      type: !isAgency ? "Agency" : undefined,
      userId: !isAgency ? +agency_id_param : undefined,
    },
  });

  const { collection: walletTransactions } =
    (data && data?.agencyTransactions?.walletTransactions) ||
    (data && data?.userWallet?.walletTransactions) ||
    {};
  const { balance } = (data && (data?.agencyTransactions || data?.userWallet)) || {};

  return (
    <>
      {!isAgency ? <ChargeBalanceModal {...{ isModalOpen, setIsModalOpen, refetch }} /> : null}
      <Styled>
        <div
          className="wallet__card"
          title={!isAgency ? formatMessage({ id: "Charge Wallet" }) : ""}
          style={{ cursor: !isAgency ? "pointer" : "auto" }}
          onClick={() => {
            if (!isAgency) {
              setIsModalOpen(true);
            }
          }}
        >
          <img src={WalletImg} alt="wallet" />
          <div>{formatMessage({ id: "Current Balance" })}</div>
          <h3>{Number(balance || 0).toLocaleString()}</h3>
          <div>
            <RiyalComponent />
            
          </div>
          {!isAgency ? (
            <div
              onClick={() => {
                if (!isAgency) {
                  setIsModalOpen(true);
                }
              }}
              style={{
                background: "#7ab3c5",
                padding: "4px 10px",
                color: "white",
                cursor: "pointer",
                margin: "5px",
                borderRadius: "5px",
              }}
            >
              {formatMessage({ id: "Charge Wallet" })}
            </div>
          ) : null}
        </div>
        <h2 className="pt-2 mb-0">{formatMessage({ id: "wallet__history" })}</h2>
        <hr />
        {walletTransactions?.length ? (
          walletTransactions.map((data) => {
            return (
              <Transaction
                key={data.id}
                data={data}
                formatMessage={formatMessage}
                language={locale}
              />
            );
          })
        ) : (
          <p className="text-center">{formatMessage({ id: "No Logs Yet" })}</p>
        )}
      </Styled>
      {/* <RequestLoader loading={loading} /> */}
    </>
  );
}

export default WalletTransactions;
