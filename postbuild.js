const fs = require("fs-extra");
const path = require("path");
const wellKnownDir = path.join(__dirname, "build", ".well-known"); // Destination: build/.well-known folder

// Paths to directories
const publicDir = path.join(__dirname, "public"); // Source: public folder
const buildDir = path.join(__dirname, "build"); // Destination: build/public folder

// Ensure the destination folder exists and copy files
fs.ensureDirSync(buildDir);

// Copy files, excluding index.html
fs.copy(
  publicDir,
  buildDir,
  {
    filter: (src) => {
      // Exclude index.html by returning false if the file is index.html
      return !src.endsWith("index.html");
    },
  },
  (err) => {
    if (err) {
      console.error("Error copying public folder:", err);
    } else {
      console.log("Public folder copied to build/public (excluding index.html)");
    }
  },
);
fs.copy(
  publicDir,
  wellKnownDir,
  {
    filter: (src) => {
      // Copy only files with .json extension
      return src;
    },
  },
  (err) => {
    if (err) {
      console.error("Error copying JSON files to .well-known folder:", err);
    } else {
      console.log("JSON files copied to build/.well-known");
    }
  },
);
