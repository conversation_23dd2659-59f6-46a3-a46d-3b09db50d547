import React, { useEffect, useState } from "react";
import { useLocation, useParams, useHistory } from "react-router-dom";
import { Helmet } from "react-helmet";
import IntlMessages from "util/IntlMessages";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import { AgencyDtails } from "gql/queries/AgencyDetails.gql";
import { AgencyUsers } from "gql/queries/AgencyUsers.gql";
import { useQuery } from "@apollo/client";
import DotsLoader from "components/shared/DotsLoader";
import { FormattedMessage, useIntl } from "react-intl";
import "photoswipe/dist/photoswipe.css";
import "photoswipe/dist/default-skin/default-skin.css";
import { AgencyDisplayData } from "./AgencyDisplayData";
import Button from "@material-ui/core/Button";
import { Tab, Tabs, TabList, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import UsersList from "./UsersList";
import { getPageFromHash, userCan } from "functions";
/**
 * @name AgencyDetails
 * @export
 * @return {JSX}
 */
export default function AgencyDetails() {
  const location = useLocation();
  const { agencyId } = useParams();
  const history = useHistory();

  const { formatMessage, locale, messages } = useIntl();
  const [page, setPage] = useState(getPageFromHash(history) || 1);
  const [limit, setLimit] = useState(50);
  const { data: agency, refetch } = useQuery(AgencyDtails, {
    variables: { id: +agencyId },
  });
  const { data: agencyUsers, refetch: agencyUsersQuery } = useQuery(AgencyUsers, {
    variables: { agencyId: +agencyId },
  });

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{formatMessage({ id: "agency.details" })}</title>
        <meta name="description" content="Carwah Agency Details" />
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="agency.details" />}
        enableBreadCrumb
        match={location}
        lastElement={agencyId || <DotsLoader />}
      />
      <div className="row">
        <Tabs className="w-100">
          <TabList>
            <Tab>
              <FormattedMessage id="basicinformation" />
            </Tab>
            {userCan("agencies.manage_users") && (
              <Tab>
                <FormattedMessage id="Agency.Users" />
              </Tab>
            )}
          </TabList>
          <TabPanel>
            {agency && (
              <div className="w-50">
                <AgencyDisplayData agencyProfile={agency} />
              </div>
            )}
          </TabPanel>
          {userCan("agencies.manage_users") && (
            <TabPanel>
              <div className="row d-flex flex-end">
                <Button
                  variant="contained"
                  color="primary"
                  className="btn btn-success"
                  onClick={() => history.push("AgencyUsers/add")}
                >
                  <IntlMessages
                    id="create.new.something"
                    values={{ something: messages?.agenciesUsers }}
                  />
                </Button>
              </div>
              <UsersList
                AgencyUsers={agencyUsers}
                setPage={setPage}
                page={page}
                setLimit={setLimit}
                limit={limit}
                agencyId={agencyId}
                refetch={agencyUsersQuery}
                isAgency={Boolean(agencyId)}
              />
            </TabPanel>
          )}
        </Tabs>
      </div>

      <div></div>
      <div className="row">
        <div className="col-md-12">
          <div className="row justify-content-end">
            <div className="col-md-2 mt-2">
              <button
                type="button"
                onClick={() => history.push(`/cw/dashboard/agencies/${agencyId}/edit`)}
                className="btn btn-primary text-center text-white"
              >
                {formatMessage({ id: "Agency.Edit" })}
              </button>{" "}
            </div>
            <div className="col-md-2 mt-2">
              <button
                onClick={() => history.push("/cw/dashboard/agencies")}
                type="button"
                className="btn btn-danger text-white text-center"
              >
                {formatMessage({ id: "button.back" })}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
