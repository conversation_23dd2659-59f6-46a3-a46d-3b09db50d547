/* eslint-disable prettier/prettier */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/button-has-type */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
/* eslint-disable spaced-comment */
/* eslint-disable radix */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-restricted-globals */
/* eslint-disable no-undefined */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable prefer-const */
/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */
import React, { useState, useRef, useMemo, useEffect } from "react";
import { useHistory, useParams } from "react-router-dom";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery, useMutation, useLazyQuery } from "@apollo/client";
import { AllAreas } from "gql/queries/Areas.queries.gql";
import {
  CreateBooking,
  EditBooking,
  RejectRentalDateExtensionRequest,
} from "gql/mutations/Rental.mutations.gql";
import { ResendRentalIntegration } from "gql/mutations/ResendRental.gql";
import { CustomerDataDisplay } from "components/CustomerDataDisplay";
import AsyncLoader from "components/AutoComplete/AsyncLoader";
import { Autocomplete } from "@material-ui/lab";
import { bookingsTypes, MonthsOfRent } from "constants/constants";
import moment from "moment";
import Map from "components/Map/MapWithSearch";
import { NotificationManager } from "react-notifications";
import "moment/locale/en-au";
import AppBar from "@material-ui/core/AppBar";
import { Tab, Tabs, TabList, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import Checkbox from "@material-ui/core/Checkbox";

// import { Tab, Tabs, TabList, TabPanel } from "react-tabs";

import {
  CircularProgress,
  FormControlLabel,
  Radio,
  FormControl,
  InputLabel,
  TextField,
  Tooltip,
} from "@material-ui/core";

import FormControlLabelContainer from "components/shared/FormControlLabelContainer";
import RadioGroupContainer from "components/shared/containers/RadioGroupContainer";
import { DateTimePickerCustom } from "components/DateTimePickerCustom";
import { GetCarProfile, GetAllAvailableCars } from "gql/queries/Cars.queries.gql";
import { GetArea } from "gql/queries/GetArea.gql";
import {
  GetRentPrice,
  GetRentalDetailsQuery,
  GetBookingsQuery,
} from "gql/queries/Rental.queries.gql";
import Box from "@material-ui/core/Box";
import { GetCustomerDetailsQuery } from "gql/queries/Users.queries.gql";
import CustomTextField from "components/Input/CustomTextField";
import FullPageLogoLoader from "components/shared/FullPageLogoLoader";
import Select from "react-select";
import { AvailableBranches } from "gql/queries/AllBranches.gql";
import { AvailableAllyCompanies } from "gql/queries/AllCompanies.gql";
import { UserWallet } from "gql/queries/CustomerWalletBalance.gql";
import { Profile } from "gql/queries/AdminProfile.gql";
import swal from "sweetalert";
import { useSnackbar } from "notistack";
import GettingCustomerDetails from "./GettingCustomerDetails";
import BookingPriceSummary from "../booking-details/BookingPriceSummary";

import styles from "./_style.module.scss";
import InstallmentsTable from "./InstallmentsTable";
import useEffects from "./useEffects";
import Modals from "./Modals";
import Header from "./Header";
import FursanVerification from "./FursanVerification";
import { ReportProblem } from "@material-ui/icons";

import { CarCouponAvailability } from "gql/queries/CarCouponAvailable.gql";
import RiyalComponent from "components/shared/RiyalComponent";
//Custom Styling
const customStyles = {
  menuPortal: (provided) => ({ ...provided, zIndex: 9999 }),
  menu: (provided) => ({ ...provided, zIndex: 9999 }),
};

function AddEditBooking() {
  //Utilities
  const { locale, formatMessage, messages } = useIntl();
  const { ally_id } = JSON.parse(localStorage.getItem("user_data"));
  const Noteref = useRef("");
  const copounref = useRef("");
  const suggestedPricePerDayRef = useRef("");
  //State
  const { bookingId } = useParams();
  const history = useHistory();
  const [allCompanies, setAllCompanies] = useState([]);
  const [customerDetails, setCustomerDetails] = useState(null);
  const [customerId, setCustomerId] = useState(null);
  const [isPickSameReturn, setIsPickSameReturn] = useState(true);
  const [pickUpCity, setPickUpCity] = useState({});
  const [dropOffCity, setDropOffCity] = useState();
  const [pickUpDate, setPickUpDate] = useState(moment().add(2, "hour"));
  const [dropOffDate, setDropOffDate] = useState(moment().add(3, "day").add(2, "hour"));
  const [selectedCar, setSelectedCar] = useState();
  const [bookingType, setBookingType] = useState("daily");
  const [deliverLat, setDeliverLat] = useState();
  const [deliverLng, setDeliverLng] = useState();
  const [insuranceId, setInsuranceId] = useState();
  const [paymentMethod, setPaymentMethod] = useState("CASH");
  // const [suggestedPricePerDay, setSuggestedPricePerDay] = useState("");
  const [deliverAddress, setDeliverAddress] = useState("");
  const [editDatedReady, setEditDatedReady] = useState(false);
  const [months, setMonths] = useState("1");
  const [monthTime, setMonthTime] = useState();
  const [changed, setChanged] = useState(false);
  const [ready, setReady] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [selectedDropoffBranch, setSelectedDropoffBranch] = useState(null);
  const [availableCarsCollection, setAvailableCarsCollection] = useState([]);
  const [avaiableCarsDD, setAvaiableCarsDD] = useState([]);
  const [extraServices, setExtraServices] = useState({});
  const [allyExtraServicesIds, setAllyExtraServicesIds] = useState([]);
  const [branchExtraServicesIds, setBranchExtraServicesIds] = useState([]);
  // const [note, setNote] = useState();
  const [extensionModalOpen, setIsExtensionModalOpen] = useState(false);
  const [distanceCarUser, setDistanceCarUser] = useState();
  const [carPlans, setCarPlans] = useState([]);
  const [plan, setPlan] = useState();
  const [deliveryPrice, setDeliveryPrice] = useState();
  const[deliveryChanged,setDeliveryChanged]=useState(false)
  const [handoverprice, setHandOverPrice] = useState(null);
  const[handoverprice2,setHandOverPrice2]=useState(null)
  const [handoverChanged,setHandoverChanged]=useState(false)
  const [isTwoWays, setIsTwoWays] = useState();
  const [FursanChecked, setFusranChecked] = useState();
  const [OpenRejectionModal, setOpenRejectionModal] = useState(false);
  const [handoverlat, setHandoverLat] = useState();
  const [carChanged, setCarChanged] = useState(false);
  const [handovderLng, setHandoverLng] = useState();
  const [clicked, setClicked] = useState(false);
  const [copounId, setCopounId] = useState();
  const [unLimited, setunLimited] = useState(false);
  const [dimmed, setDimmed] = useState(false);
  const [isDelivery, setIsDelivery] = useState(false);
  const [withInstallment, setwithInstallment] = useState(false);
  const [copounCode, setCouponCode] = useState();
  const [couponAvailabilty, setCouponAvailability] = useState();
  const [insuranceName, setInsuranceName] = useState("");
  const [InsuranceChanged,setInsuranceChanged]=useState(false)
  //Variables

  //GQL
  const [createBookingMutation, { loading: creatingRental }] = useMutation(CreateBooking);
  const [EditBookingMutation, { loading: editingRental }] = useMutation(EditBooking);
  const [resendRental] = useMutation(ResendRentalIntegration);
  const { data: userInfo } = useQuery(Profile);

  const { refetch: refetchAllBookings } = useQuery(GetBookingsQuery, {
    skip: true,
  });

  const { data: rentalDetails, refetch: refetchBooking } = useQuery(GetRentalDetailsQuery, {
    skip: !bookingId,
    variables: { id: bookingId },
    fetchPolicy: "network-only",
    errorPolicy: "all",
    onError(error) {
      NotificationManager.error(error.message);
    },
  });
  const [getAreasQuery, { data: AreasRes, loading: gettingAreas }] = useLazyQuery(AllAreas);
  const [getCarCouponAvailable] = useLazyQuery(CarCouponAvailability);

  // Customer Data Request by id for edit mode
  const { data: customerDetailsRes } = useQuery(
    GetCustomerDetailsQuery,

    {
      skip: !rentalDetails?.rentalDetails,
      variables: { id: rentalDetails?.rentalDetails?.userId },
      errorPolicy: "all",
      onError(error) {
        NotificationManager.error(error.message);
      },
    },
  );
  const { data: walletBalance } = useQuery(UserWallet, {
    skip: !rentalDetails?.rentalDetails,
    variables: { userId: +rentalDetails?.rentalDetails?.userId },
  });

  const [getBranches, { data: branches }] = useLazyQuery(AvailableBranches, {
    fetchPolicy: "no-cache",
  });

  const isHandover =
    (getDropoffBranches()?.length &&
      (selectedBranch?.id != selectedDropoffBranch?.id ||
        (selectedBranch?.value &&
          selectedDropoffBranch &&
          !selectedDropoffBranch?.id &&
          selectedBranch?.value != selectedDropoffBranch &&
          selectedBranch?.value != selectedDropoffBranch?.value))) ||
    (!changed &&
      rentalDetails?.rentalDetails?.branchId != rentalDetails?.rentalDetails?.dropOffBranchId);

  // Car Details Request
  const [getCarDetails, { data: carDetailsRes }] = useLazyQuery(GetCarProfile, {
    skip: !rentalDetails,
    variables: { id: rentalDetails?.rentalDetails?.carId },
  });
  const {
    data: BookingPriceRes,
    loading: calculatingPrice,
    refetch: recalculateRentPrice,
  } = useQuery(GetRentPrice, {
    skip:
      (!insuranceId && bookingType != "rent-to-own") ||
      !selectedCar ||
      (bookingId && !rentalDetails) ||
      (bookingType == "rent-to-own" && !plan?.id) ||
      (!changed && !insuranceId) || !selectedBranch ,
    // !changed,
    errorPolicy: "all",
    onError(error) {
      if (error.message?.includes("can not be applied")) {
        NotificationManager.error(
          <FormattedMessage id="Installment payments are not permitted for bookings with a duration less than 60 days or multiple of 30 days" />,
        );
      } else {
        NotificationManager.error(error.message);
      }
      setDimmed(true);
    },
    onCompleted: () => {
      setDimmed(false);
    },
    variables: {
      carId: selectedCar?.id,
      isCarChanged: carChanged,
      isUnlimited: unLimited,
      paymentMethod,
      deliveryPrice: isDelivery ? deliveryPrice : undefined,
      handoverPrice: isHandover || isTwoWays ? handoverprice2 || rentalDetails?.rentalDetails?.handoverPrice : undefined,
      ownCarPlanId: bookingType == "rent-to-own" ? (plan?.id ? plan?.id : undefined) : undefined,
      handoverBranch:
        isHandover && !isTwoWays
          ? selectedDropoffBranch?.value || selectedDropoffBranch
          : undefined,
      deliverLat,
      deliverLng,
      couponId: copounId ? +copounId : undefined,

      deliveryType: isDelivery && isTwoWays ? "two_ways" : isDelivery ? "one_way" : "no_delivery",
      dropOffDate:
        bookingType == "rent-to-own"
          ? undefined
          : moment(dropOffDate).locale("en").format("DD/MM/YYYY"),
      dropOffTime:
        bookingType == "rent-to-own"
          ? undefined
          : `${moment(dropOffDate).locale("en").format("HH:mm")}:00`,
      insuranceId: +insuranceId || undefined,
      pickUpDate: moment(pickUpDate?._id || pickUpDate)
        .locale("en")
        .format("DD/MM/YYYY"),
      pickUpTime: `${moment(pickUpDate?._id || pickUpDate)
        .locale("en")
        .format("HH:mm")}:00`,
      allyExtraServices: allyExtraServicesIds?.length ? [...new Set(allyExtraServicesIds)] : null,
      branchExtraServices: branchExtraServicesIds?.length
        ? [...new Set(branchExtraServicesIds)]
        : null,
      usedPrice: !changed ? rentalDetails?.rentalDetails?.pricePerDay : undefined,
      // withWallet: !!rentalDetails?.rentalDetails?.walletTransactions,
      walletPaidAmount: rentalDetails?.rentalDetails?.walletTransactions?.amount
        ? rentalDetails?.rentalDetails?.walletTransactions?.amount
        : undefined,
      suggestedPrice: rentalDetails?.rentalDetails?.suggestedPrice || undefined,
      isEdit: !!bookingId,
      rentalId: bookingId || undefined,
      payWithInstallments:
        bookingType != "rent-to-own" &&
        (withInstallment || Boolean(rentalDetails?.rentalDetails?.installments?.length)),
    },
  });

  const isRentalInstallment = BookingPriceRes?.aboutRentPrice?.installmentsBreakdown?.length;
  const isPaidInstallment = BookingPriceRes?.aboutRentPrice?.installmentsBreakdown?.find(
    (i) => i.status === "paid",
  );
  // Request => Available Cars
  const requestCarsvariables = {
    pickStartDate:
      rentalDetails?.rentalDetails && bookingId
        ? moment(pickUpDate?._id || pickUpDate)
            .locale("en")
            .format("DD/MM/YYYY")
        : moment(pickUpDate?._id || pickUpDate)
            .locale("en")
            .format("DD/MM/YYYY"),

    pickEndDate:
      moment(dropOffDate?._id || dropOffDate)
        .locale("en")
        .diff(moment(), "days") < 0
        ? null
        : dropOffDate?._id
        ? moment(dropOffDate?._id || dropOffDate)
            .locale("en")
            .format("DD/MM/YYYY")
        : undefined,
    pickUpLocationId: +pickUpCity?.id,
    dropOffLocationId: isPickSameReturn ? +pickUpCity?.id : +dropOffCity?.id,
    isActive: true,
  };
  const allBranches =
    branches?.availableBranches.collection.map((branch) => ({
      value: branch?.id,
      label: branch?.[`${locale}Name`],
      canHandover: branch?.canHandover,
      canDelivery: branch?.canDelivery,
      branchDeliveryPrices: [...branch?.branchDeliveryPrices],
    })) || [];

  const { data: allyCompanies, loading: loadingAllyCompanies } = useQuery(AvailableAllyCompanies, {
    variables: {
      limit: 1000,
      isRentToOwn: bookingType == "rent-to-own" ? true : undefined,
      pickupCityId: !isPickSameReturn
        ? +pickUpCity?.id || +rentalDetails?.rentalDetails?.pickUpCityId
        : undefined,
      dropoffCityId: !isPickSameReturn
        ? +dropOffCity?.id || +rentalDetails?.rentalDetails?.dropOffCityId
        : undefined,
      cityId: isPickSameReturn
        ? +pickUpCity?.id || +rentalDetails?.rentalDetails?.pickUpCityId
        : undefined,
      pickStartDate:
        rentalDetails?.rentalDetails && bookingId
          ? moment(pickUpDate?._id || pickUpDate)
              .locale("en")
              .format("DD/MM/YYYY")
          : moment(pickUpDate?._id || pickUpDate)
              .locale("en")
              .format("DD/MM/YYYY"),
    },
  });
  const [getCars, { data: CarsRes, loading: gettingCars }] = useLazyQuery(GetAllAvailableCars, {
    fetchPolicy: "no-cache",
  });
  const [getArea, { data: area }] = useLazyQuery(GetArea);
  const [mapChange, setMapChange] = useState(false);
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);
  const [FursanVerified, setFursanVerified] = useState();
  const [rejectRentalDateExtensionRequest] = useMutation(RejectRentalDateExtensionRequest);
  const [isopen, setIsOpen] = useState();
  //Functions
  const clearBranchSelection = () => {
    setSelectedBranch(null);
    setInsuranceId(null);
    setSelectedDropoffBranch(null);
    setAllyExtraServicesIds([]);
    setBranchExtraServicesIds([]);
  };

  //LifeCycle
  useEffects({
    refetchBooking,
    insuranceId,
    setHandOverPrice2,
    InsuranceChanged,
    setFusranChecked,
    AreasRes,
    getCarDetails,
    getAreasQuery,
    allyCompanies,
    rentalDetails,
    locale,
    setAllCompanies,
    area,
    changed,
    setPickUpCity,
    CarsRes,
    setAvailableCarsCollection,
    setAvaiableCarsDD,
    carName,
    carDetailsRes,
    bookingType,
    pickUpDate,
    dropOffDate,
    setMonths,
    setMonthTime,
    setDropOffDate,
    setDropOffCity,
    setSelectedDropoffBranch,
    monthTime,
    ready,
    editDatedReady,
    setDeliveryPrice,
    setHandOverPrice,
    setInsuranceId,
    setIsTwoWays,
    setPlan,
    setCustomerId,
    setCopounId,
    copounref,
    setCouponCode,
    setunLimited,
    dropOffCity,
    pickUpCity,
    isPickSameReturn,
    selectedDropoffBranch,
    suggestedPricePerDayRef,
    setBookingType,
    setEditDatedReady,
    setPickUpDate,
    setPaymentMethod,
    setReady,
    setDistanceCarUser,
    setSelectedCar,
    setCarPlans,
    setDeliverLng,
    setDeliverLat,
    selectedCompany,
    getBranches,
    setSelectedBranch,
    selectedBranch,
    allBranches,
    setSelectedCompany,
    allCompanies,
    branchExtraServicesIds,
    setBranchExtraServicesIds,
    allyExtraServicesIds,
    setAllyExtraServicesIds,
    setExtraServices,
    branches,
    selectedCar,
    requestCarsvariables,
    getCars,
    mapChange,
    deliverLat,
    handoverprice,
    distanceCarUser,
    deliverLng,
    setIsPickSameReturn,
    setIsDelivery,
    carId: selectedCar?.id,
    isRentalInstallment,
    setwithInstallment,
    withInstallment,
    BookingPriceRes,
    isHandover,
    setInsuranceName,
  });

  function a11yProps(index) {
    return {
      id: `wrapped-tab-${index}`,
      "aria-controls": `wrapped-tabpanel-${index}`,
    };
  }

  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  function getDropoffBranches() {
    if (selectedCar && dropOffCity) {
      const availableHandoverBranches = selectedCar?.branch?.availableHandoverBranches;
      const filteredBranches = availableHandoverBranches
        ?.filter((b) => b.areaId == dropOffCity?.id)
        .map((i) => ({
          label: i.name,
          value: i.id,
          ...i,
        }));
      if (pickUpCity?.id == dropOffCity?.id) filteredBranches.push(selectedBranch);

      return [...filteredBranches];
    }
  }

  const getRentalAudits = () => {
    setOpenTimeLineModal(true);
  };
  //Form Submit
  useEffect(() => {
    if (!BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
      setCouponAvailability(null);
    } else {
      setCouponAvailability("Invalid coupon");
    }
  }, [BookingPriceRes]);
  async function handleSubmitRent(e, params) {
    if (e) e.preventDefault();
    setClicked(true);

    const variables = {
      carId: selectedCar?.id,
      paymentMethod,
      pickUpCityId: pickUpCity?.id,
      pickUpDate: pickUpDate.locale("en").format("DD/MM/YYYY"), // convertLocalTimeToUtc(pickUpDate), // moment(pickUpDate?._id || pickUpDate).format("DD/MM/YYYY"),
      pickUpTime: pickUpDate.locale("en").format("HH:mm:ss"), // convertLocalTimeToUtc(pickUpDate), // `${moment(pickUpDate?._id || pickUpDate).format("HH:mm")}:00`,
      dropOffBranchId:
        (!changed
          ? +rentalDetails?.rentalDetails?.dropOffBranchId
          : +selectedDropoffBranch?.value ||
            +selectedDropoffBranch ||
            +selectedBranch?.value ||
            +selectedBranch) || undefined,
      dropOffCityId: isPickSameReturn ? pickUpCity?.id : +dropOffCity?.id,
      dropOffDate:
        bookingType == "rent-to-own" ? undefined : dropOffDate.locale("en").format("DD/MM/YYYY"), // convertLocalTimeToUtc(dropOffDate), // moment(dropOffDate).format("DD/MM/YYYY"),
      dropOffTime:
        bookingType == "rent-to-own" ? undefined : dropOffDate.locale("en").format("HH:mm:ss"), // convertLocalTimeToUtc(dropOffDate),
      insuranceId,
      userId: customerDetails?.users?.collection?.[0]?.id,
      deliverLat: isDelivery ? deliverLat || rentalDetails?.rentalDetails?.deliverLat : null,
      deliverLng: isDelivery ? deliverLng || rentalDetails?.rentalDetails?.deliverLng : null,
      deliveryPrice: isDelivery ? deliveryPrice : undefined,
      handoverPrice: isHandover || isTwoWays ? handoverprice2 : undefined,
      deliverType: isDelivery && isTwoWays ? "two_ways" : isDelivery ? "one_way" : "no_delivery",
      deliverAddress: isDelivery
        ? deliverAddress || rentalDetails?.rentalDetails?.deliverAddress || pickUpCity?.id
        : "",
      allyExtraServices: [...new Set(allyExtraServicesIds)],
      branchExtraServices: [...new Set(branchExtraServicesIds.map((b) => +b))],
      notes: Noteref.current,
      isUnlimited: unLimited,
      ownCarPlanId: bookingType == "rent-to-own" ? (plan?.id ? plan?.id : undefined) : undefined,
      handoverAddress: isDelivery
        ? deliverAddress || rentalDetails?.rentalDetails?.deliverAddress || pickUpCity?.id
        : "",
      handoverLat:
        isHandover || isTwoWays
          ? handoverlat ||
            rentalDetails?.rentalDetails?.handoverLat ||
            deliverLat ||
            rentalDetails?.rentalDetails?.deliverLat
          : undefined,
      handoverLng:
        isHandover || isTwoWays
          ? handovderLng ||
            rentalDetails?.rentalDetails?.handoverLng ||
            deliverLng ||
            rentalDetails?.rentalDetails?.deliverLng
          : undefined,
      withInstallment: !bookingId && bookingType != "rent-to-own" ? withInstallment : undefined,
    };

    if (suggestedPricePerDayRef?.current?.length) {
      variables.suggestedPrice = +suggestedPricePerDayRef.current;
    } else {
      variables.suggestedPrice = 0;
    }

    if (bookingId) {
      if (
        rentalDetails?.rentalDetails?.allyRentalRejections?.find(
          (reason) => reason.branchId == selectedCar?.branch?.id,
        )
      ) {
        swal({
          title: formatMessage({
            id: "You've chosen an ally who has previously declined this booking",
          }),
          text: formatMessage({ id: "Would you like to proceed?" }),
          icon: "warning",
          buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
          dangerMode: true,
        }).then((willDelete) => {
          if (willDelete) {
            setDimmed(true);
            variables.rentalId = +bookingId;
            if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
              swal({
                title: "",
                text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
                icon: "warning",
                buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
                dangerMode: true,
              }).then((willDelete) => {
                if (willDelete) {
                  variables.couponId = undefined;
                  EditBookingMutation({
                    variables,
                  })
                    .then(() => {
                      NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                      recalculateRentPrice();
                      refetchBooking().then(() => {
                        setTimeout(() => {
                          setDimmed(false);
                        }, 1500);
                      });
                    })
                    .catch((err) => {
                      setDimmed(false);
                      NotificationManager.error(err.message);
                    });
                } else {
                  return;
                }
              });
            } else {
              variables.couponId=copounId ? +copounId : undefined
              EditBookingMutation({
                variables,
              })
                .then(() => {
                  NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                  recalculateRentPrice();
                  refetchBooking().then(() => {
                    setTimeout(() => {
                      setDimmed(false);
                    }, 1500);
                  });
                })
                .catch((err) => {
                  setDimmed(false);
                  NotificationManager.error(err.message);
                });
            }
          }
        });
      } else {
        if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
          swal({
            title: "",
            text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
            icon: "warning",
            buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
            dangerMode: true,
          }).then((willDelete) => {
            if (willDelete) {
              variables.couponId = undefined;
              setDimmed(true);
              variables.rentalId = +bookingId;
              EditBookingMutation({
                variables,
              })
                .then(() => {
                  NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                  recalculateRentPrice();
                  refetchBooking().then(() => {
                    setTimeout(() => {
                      setDimmed(false);
                    }, 1500);
                  });
                })
                .catch((err) => {
                  setDimmed(false);
                  NotificationManager.error(err.message);
                });
            } else {
              return;
            }
          });
        } else {
          setDimmed(true);
          variables.rentalId = +bookingId;
          variables.couponId = +copounId;

          EditBookingMutation({
            variables,
          })
            .then(() => {
              NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
              recalculateRentPrice();
              refetchBooking().then(() => {
                setTimeout(() => {
                  setDimmed(false);
                }, 1500);
              });
            })
            .catch((err) => {
              setDimmed(false);
              NotificationManager.error(err.message);
            });
        }
      }
    } else {
      if (!dropOffCity && !isPickSameReturn) {
        return;
      }
      setDimmed(true);
      variables.loyaltyType = FursanVerified && FursanChecked ? "alfursan" : undefined;
      // return
      if (BookingPriceRes?.aboutRentPrice?.couponErrorMessage) {
        swal({
          title: "",
          text: BookingPriceRes?.aboutRentPrice?.couponErrorMessage,
          icon: "warning",
          buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
          dangerMode: true,
        }).then((willDelete) => {
          if (willDelete) {
            variables.couponId = undefined;
            setDimmed(true);
            createBookingMutation({
              variables,
            })
              .then((res) => {
                NotificationManager.success(formatMessage({ id: "success.create.rental" }));
                refetchAllBookings().finally(() => {
                  setTimeout(() => {
                    setDimmed(false);
                    history.push("/cw/dashboard/bookings");
                  }, 1500);
                });
              })
              .catch((err) => {
                setDimmed(false);
                NotificationManager.error(err.message);
              });
          } else {
            setDimmed(false);

            return;
          }
        });
      } else {
        setDimmed(true);

        variables.couponId = +copounId;

        createBookingMutation({
          variables,
        })
          .then(() => {
            NotificationManager.success(formatMessage({ id: "success.create.rental" }));
            refetchAllBookings().finally(() => {
              setTimeout(() => {
                setDimmed(false);
                history.push("/cw/dashboard/bookings");
              }, 1500);
            });
          })
          .catch((err) => {
            setDimmed(false);
            NotificationManager.error(err.message);
          });
      }
    }
  }
  const OpenAlert = (val) => {
    swal({
      title: formatMessage({
        id: "Editing this booking period will close the pending extensions",
      }),
      icon: "warning",
      buttons: [formatMessage({ id: "cancel" }), formatMessage({ id: "Confirm" })],
      dangerMode: true,
    }).then((willDelete) => {
      if (willDelete) {
        rejectRequestHandler(rentalDetails?.rentalDetails?.pendingExtensionRequest?.id, val);
      }
    });
  };
  function rejectRequestHandler(id, val) {
    rejectRentalDateExtensionRequest({ variables: { rentalExtensionId: id } })
      .then((res) => {
        if (res.data.rejectRentalDateExtensionRequest.status === "success") {
          clearBranchSelection();
          setPickUpCity(null);
          setTimeout(() => {
            refetchBooking();

            setPickUpDate(val);
            if (bookingType == "rent-to-own") {
              setDropOffDate(moment(val).locale("en").add(plan.noOfMonths, "months"));
            } else {
              setDropOffDate(val);
            }
          }, 100);
          NotificationManager.success(<FormattedMessage id="Done successfully" />);
        } else {
          NotificationManager.error(<FormattedMessage id=" Error " />);
        }
      })
      .catch((err) => NotificationManager.error(err.message));
  }
  const shareLocation = () => {
    const url = bookingId
      ? `https://www.google.com/maps?q=${rentalDetails?.rentalDetails.deliverLat},${rentalDetails?.rentalDetails?.deliverLng}`
      : `https://www.google.com/maps?q=${deliverLat},${deliverLng}`;
    navigator.clipboard.writeText(url).then(() => {
      swal({
        title: formatMessage({ id: "Link Copied" }),

        icon: "success",
      });
    });
  };
  //JSX
  return (
    <>
      {(editingRental || creatingRental) && <FullPageLogoLoader />}
      <Header
        {...{
          formatMessage,
          bookingId,
          location,
          rentalDetails,
          resendRental,
          NotificationManager,
          refetchBooking,
          messages,
          userInfo,
          getRentalAudits,
          setIsExtensionModalOpen,
        }}
      />
      {!bookingId && (
        <div className={`mt-4 ${styles.customerDetails}`}>
          <h3>
            <FormattedMessage id="rental.enterphone" />
          </h3>
          <GettingCustomerDetails
            setCustomerDetails={setCustomerDetails}
            setCustomerId={setCustomerId}
            setFusranChecked={setFusranChecked}
            setFursanVerified={setFursanVerified}
          />
        </div>
      )}
      <Tabs value={value} onChange={handleChange}>
        <div>
          <AppBar position="static" className="mt-2 tabview">
            <TabList>
              <Tab>
                <FormattedMessage id="basicinformation" />
              </Tab>
              {rentalDetails?.rentalDetails?.installments?.length ? (
                <Tab>
                  <FormattedMessage id="Rental Installments" />
                </Tab>
              ) : null}
            </TabList>
          </AppBar>

          <TabPanel>
            {(customerDetails || customerDetailsRes) && (
              <div className="row p-1" style={{ marginTop: "0px", flexWrap: "nowrap" }}>
                <div
                  className="d-flex flex-column col-md-7"
                  style={{ display: "grid !important", gridRowGap: "10px" }}
                >
                  <div
                    className="mt-3 mb-1"
                    style={{ gap: "20px" }}
                    onChange={(e) => {
                      const { value } = e.target || {};
                      setChanged(true);
                      setBookingType(value);
                      switch (value) {
                        case "daily":
                          setMonths(0);
                          break;
                        case "monthly":
                          setMonths(3);

                          setDropOffDate(
                            moment(pickUpDate)
                              .locale("en")
                              .add(30 * 3, "days"),
                          );

                          break;
                        default:
                          null;
                      }
                      clearBranchSelection();

                      setSelectedCompany(null);
                      setBranchExtraServicesIds([]);
                      setSelectedCar(null);
                    }}
                  >
                    <h4 style={{ fontWeight: "bold" }}>
                      <FormattedMessage id="rental.bookingType" />
                    </h4>
                    <div className="d-flex" style={{ gap: "0.1rem" }}>
                      {bookingsTypes.map((type) => (
                        <div className="form-check form-check-inline" key={type.id}>
                          <input
                            className="form-check-input"
                            type="radio"
                            checked={type.name === bookingType}
                            name="bookingType"
                            id={type.name}
                            disabled={
                              (bookingId && bookingType == "rent-to-own") ||
                              (bookingId && type?.name == "rent-to-own")
                            }
                            value={type.name}
                            onChange={() => {
                              setChanged(true);
                            }}
                          />
                          <label className="form-check-label p-0 m-2" htmlFor={type.name}>
                            <FormattedMessage id={type?.name} />
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  {!bookingId && bookingType != "rent-to-own" ? (
                    <div style={{ marginBottom: "15px" }}>
                      <input
                        key={`${isRentalInstallment}`}
                        id="isInstallment"
                        style={{ cursor: "pointer" }}
                        type="checkbox"
                        defaultChecked={isRentalInstallment}
                        checked={withInstallment}
                        onChange={(e) => {
                          const { checked } = e.target || {};
                          setChanged(true);
                          setwithInstallment(checked);
                          if (checked) {
                            setDropOffDate(
                              moment(pickUpDate)
                                .locale("en")
                                .add(30 * 3, "days"),
                            );
                          }
                        }}
                        disabled={isPaidInstallment}
                      />
                      <label className="form-check-label p-0 m-2" htmlFor="isInstallment">
                        <FormattedMessage id="Installments.booking" />
                      </label>
                    </div>
                  ) : null}

                  <div>
                    <h4 style={{ fontWeight: "bold" }}>
                      <FormattedMessage id="rental.bookingTiming" />
                    </h4>

                    {bookingId && gettingAreas && (
                      <>
                        <CircularProgress />
                      </>
                    )}
                    {(!bookingId || !(bookingId && !editDatedReady)) && (
                      <div className="d-flex justify-content-between flex-wrap">
                        <DateTimePickerCustom
                          autoOk
                          label={formatMessage({ id: "rental.pickupDateTime" })}
                          maxDate={
                            bookingType == "rent-to-own"
                              ? moment(new Date()).locale("en").add(20, "days")
                              : moment(new Date()).locale("en").add(20, "year")
                          }
                          value={pickUpDate}
                          onChange={(val) => {
                            setChanged(true);
                            if (rentalDetails?.rentalDetails?.hasPendingExtensionRequests) {
                              OpenAlert(val);
                              return;
                            }
                            clearBranchSelection();
                            setPickUpCity(null);
                            setTimeout(() => {
                              setPickUpDate(val);
                              if (bookingType == "rent-to-own") {
                                setDropOffDate(
                                  moment(val).locale("en").add(plan.noOfMonths, "months"),
                                );
                              }
                            }, 100);
                          }}
                          minDateMessage={formatMessage({ id: "pleaseSelectSuitablePicupDate" })}
                        />
                        {bookingType !== "monthly" && bookingType != "rent-to-own" && (
                          <DateTimePickerCustom
                            autoOk
                            disabled={bookingType == "rent-to-own"}
                            value={dropOffDate}
                            label={formatMessage({ id: "rental.dropoffDateTime" })}
                            onChange={(val) => {
                              if (rentalDetails?.rentalDetails?.hasPendingExtensionRequests) {
                                OpenAlert(val);
                                return;
                              }
                              setChanged(true);
                              setTimeout(() => {
                                setDropOffDate(val);
                              }, 100);
                            }}
                            minDate={pickUpDate}
                            minDateMessage={formatMessage({
                              id: "validation.fromMustBeLessThanTo",
                            })}
                          />
                        )}
                      </div>
                    )}
                    {bookingType === "monthly" && (
                      <div className="row">
                        <div className="col-md-6">
                          <FormControl variant="standard">
                            <InputLabel id="demo-simple-select-outlined-label">
                              <FormattedMessage id="months.count" />
                            </InputLabel>
                            <Select
                              key={months}
                              placeholder={formatMessage({ id: "months.count" })}
                              options={MonthsOfRent}
                              value={MonthsOfRent.find((month) => +month.value == +months)}
                              onChange={(selection) => {
                                setMonths(+selection.value);
                                setDropOffDate(() =>
                                  moment(pickUpDate)
                                    .locale("en")
                                    .add(+selection.value * 30, "days"),
                                );
                                setChanged(true);
                              }}
                              getOptionLabel={(options) => <FormattedMessage id={options.label} />}
                            ></Select>
                          </FormControl>
                        </div>
                        <div className="col-md-6">
                          <TextField
                            key={`${dropOffDate}-${monthTime}`}
                            id="time"
                            label={formatMessage({ id: "rental.dropoffTime" })}
                            type="time"
                            defaultValue={
                              monthTime && monthTime !== "Invalid date"
                                ? monthTime
                                : moment(dropOffDate).locale("en").format("HH:mm")
                            }
                            InputLabelProps={{ shrink: true }}
                            onChange={(e) => {
                              setMonthTime(e.target.value);
                            }}
                            inputProps={{ step: 300 * 6 }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  <div>
                    <h4 style={{ fontWeight: "bold" }}>
                      <FormattedMessage id="rental.bookingLocation" />
                    </h4>
                    <div
                      className="form-check mt-3 d-flex align-items-center justify-content-between"
                      style={{ padding: "0 15px" }}
                    >
                      <div>
                        <input
                          type="checkbox"
                          className="form-check-input"
                          id="isPickSameReturn"
                          checked={isPickSameReturn}
                          data-testid="isPickSameReturn"
                          onClick={(e) => {
                            setIsPickSameReturn(e.target.checked);
                            if (e.target.checked) {
                              setDropOffCity(pickUpCity);
                              setSelectedDropoffBranch(null);
                              setHandOverPrice(null);
                            } else {
                              setIsTwoWays(false);
                            }
                          }}
                        />
                        <label
                          className="form-check-label p-0 m-2"
                          htmlFor="isPickSameReturn"
                          style={{ transform: "translateY(-8px)" }}
                        >
                          <FormattedMessage id="same.as.pickup.location" />
                        </label>
                      </div>
                      {isDelivery ? (
                        <div>
                          <label
                            style={{ margin: 0, padding: 0, cursor: "pointer" }}
                            title={formatMessage({ id: "copylink" })}
                            onClick={shareLocation}
                          >
                            <i class="ti-files" aria-hidden="true"></i>
                          </label>
                        </div>
                      ) : null}
                    </div>
                    {isDelivery && (
                      <div
                        style={{
                          minHeight: "300px",
                          width: "100%",
                          position: "relative",
                          marginBottom: "30px",
                        }}
                      >
                        <Map
                          latitude={deliverLat}
                          longitude={deliverLng}
                          setLatitude={(lat) => setDeliverLat(lat)}
                          setLongitude={(lng) => setDeliverLng(lng)}
                          centerlat={deliverLat}
                          centerlng={deliverLng}
                          setMapChange={setMapChange}
                          mapChange={mapChange}
                          isBooking
                          setDeliverAddress={setDeliverAddress}
                        />
                      </div>
                    )}
                    {!gettingAreas || (bookingId && !gettingAreas && pickUpCity) ? (
                      //Edit - make sure that city exists before rendering
                      <Autocomplete
                        id="pickup-location"
                        options={AreasRes?.areas || []}
                        className="mt-2 mb-2"
                        getOptionLabel={(option) => option?.[`${locale}Name`]}
                        value={pickUpCity}
                        disableClearable
                        onChange={(e, newValue) => {
                          setDeliverLat(newValue.centerLat);
                          setDeliverLng(newValue.centerLng);
                          setCopounId(null);
                          setCouponCode(null);
                          clearBranchSelection();
                          setDeliveryPrice(null);
                          setPickUpCity(newValue);
                          if (isPickSameReturn) setDropOffCity(newValue);
                          setMapChange(!mapChange);
                          setHandOverPrice(null);
                        }}
                        loading={gettingAreas}
                        disabled={!customerId}
                        renderInput={(params) => (
                          <AsyncLoader
                            params={params}
                            labelId="rental.pickupLocation"
                            loading={gettingAreas}
                            value={pickUpCity}
                          />
                        )}
                      />
                    ) : (
                      <CircularProgress />
                    )}
                    {(!bookingId && !isPickSameReturn) ||
                    (bookingId && !isPickSameReturn && dropOffCity) ||
                    (bookingId && !isPickSameReturn && isDelivery) ? (
                      <Autocomplete
                        style={{ marginTop: "25px" }}
                        id="dropoff-location"
                        className="mb-2"
                        isRequired={!isPickSameReturn}
                        options={
                          AreasRes?.areas?.filter((city) => +city?.id !== pickUpCity?.id) || []
                        }
                        getOptionLabel={(option) => option?.[`${locale}Name`]}
                        value={dropOffCity}
                        disableClearable
                        onChange={(e, val) => {
                          setHandoverLat(val.centerLat);
                          setHandoverLng(val.centerLng);
                          setDropOffCity(val);
                          setSelectedCompany(null);
                          setSelectedBranch(null);
                          setSelectedDropoffBranch(null);
                          setSelectedCar(null);
                          setHandOverPrice(null);
                        }}
                        loading={gettingAreas}
                        disabled={!customerId}
                        renderInput={(params) => (
                          <TextField
                            error={clicked && Boolean(!dropOffCity)}
                            {...params}
                            label={<FormattedMessage id="dropoff-location" />}
                            variant="outlined"
                            fullWidth
                            helperText={
                              !dropOffCity && !isPickSameReturn && clicked ? (
                                <FormattedMessage id="thisfieldisrequired" />
                              ) : null
                            }
                          />
                        )}
                      />
                    ) : null}
                  </div>
                  {pickUpCity ? (
                    <>
                      <div>
                        <div className="mb-3">
                          <div style={{ display: "flex", gap: "10px" }}>
                            <h4 style={{ alignSelf: "center", fontWeight: "bold" }}>
                              <FormattedMessage id="rental.rentalCar" />
                            </h4>
                            {rentalDetails?.rentalDetails?.allyRentalRejections?.length ? (
                              <Tooltip
                                placement="right"
                                title={<FormattedMessage id="Ally Declined" />}
                              >
                                <button
                                  style={{ background: "none", border: "none", cursor: "pointer" }}
                                  onClick={() => {
                                    setOpenRejectionModal(true);
                                  }}
                                >
                                  <ReportProblem
                                    style={{
                                      cursor: "pointer",
                                      fontSize: "18px",
                                      color: "#5d89d8",
                                    }}
                                  />
                                </button>
                              </Tooltip>
                            ) : null}
                          </div>

                          {!ally_id ? (
                            <>
                              <h6 className="mt-1 mb-3">
                                {formatMessage({ id: "selecting.company" })}
                              </h6>
                              <Select
                                key={`${pickUpCity} ${dropOffCity} ${pickUpCity?.id} ${dropOffCity?.id}`}
                                options={allCompanies}
                                value={allCompanies?.find(
                                  (val) => val.value == selectedCompany?.value,
                                )}
                                placeholder={formatMessage({ id: "selecting.company" })}
                                onChange={(selection) => {
                                  clearBranchSelection();
                                  setCopounId(null);
                                  setSelectedCompany(selection);
                                  setBranchExtraServicesIds([]);
                                  setCopounId(null);
                                  setCouponCode(null);
                                  setSelectedCar(null);
                                }}
                                isLoading={!!loadingAllyCompanies}
                                className="dropdown-select"
                                styles={customStyles}
                              />
                            </>
                          ) : null}
                        </div>
                        {(!bookingId && selectedCompany?.value) ||
                        (bookingId && selectedCompany?.value && allBranches && rentalDetails) ? (
                          <div className="mt-2 mb-3">
                            <>
                              <h6 className="mt-1 mb-3">
                                {formatMessage({
                                  id: isPickSameReturn
                                    ? "selecting.branch"
                                    : "select.Pickup branch",
                                })}
                              </h6>
                              <Select
                                key={`${selectedCompany} ${selectedBranch}`}
                                options={allBranches}
                                value={selectedBranch}
                                placeholder={formatMessage({
                                  id: isPickSameReturn
                                    ? "selecting.branch"
                                    : "select.Pickup branch",
                                })}
                                onChange={(selection) => {
                                  setSelectedBranch(selection);
                                  setCopounId(null);
                                  setCouponCode(null);
                                  setSelectedCar(null);
                                }}
                                className="dropdown-select"
                                styles={customStyles}
                              />
                            </>
                          </div>
                        ) : null}

                        {(!bookingId && selectedBranch) || (bookingId && selectedBranch) ? (
                          <div style={{ position: "relative" }}>
                            <h6 className="mt-1 mb-3">
                              {formatMessage({
                                id: "select car",
                              })}
                            </h6>
                            <Select
                              key={`${selectedBranch}`}
                              options={avaiableCarsDD}
                              value={avaiableCarsDD?.find((val) => +val.value === +selectedCar?.id)}
                              placeholder={formatMessage({
                                id: "select car",
                              })}
                              isLoading={!!gettingCars}
                              onChange={(sel) => {
                                if (sel.value == rentalDetails?.rentalDetails?.carId) {
                                  setCarChanged(false);
                                } else {
                                  setCarChanged(true);
                                }
                                const selectedCar = availableCarsCollection?.find(
                                  (car) => +car.id === +sel?.value,
                                );
                                setCopounId(null);
                                setCouponCode(null);
                                // setPlan(selectedCar?.ownCarDetail?.ownCarPlans[0]);
                                setCarPlans(
                                  selectedCar?.ownCarDetail?.ownCarPlans?.filter(
                                    (plan) => plan?.isActive,
                                  ),
                                );
                                setDistanceCarUser(+selectedCar.distanceBetweenCarUser);
                                setunLimited(false);
                                setSelectedCar(selectedCar);
                                setSelectedDropoffBranch(null);
                                setChanged(true);
                                setInsuranceId(null);
                                setTimeout(() => {
                                  setIsDelivery(false);
                                }, []);
                              }}
                            />
                          </div>
                        ) : null}
                        {!isPickSameReturn &&
                        !isTwoWays &&
                        getDropoffBranches()?.length &&
                        ((!bookingId && selectedCar && bookingType != "rent-to-own") ||
                          (bookingId && selectedCar && bookingType != "rent-to-own")) ? (
                          <div className="mt-3 mb-3">
                            <>
                              <h6
                                className="mt-1 mb-3"
                                style={{ fontWeight: !selectedDropoffBranch ? "bold" : "" }}
                              >
                                {formatMessage({
                                  id: "select.Dropoff branch",
                                })}
                              </h6>
                              <Select
                                key={`${selectedCompany} ${selectedBranch} ${selectedCar} ${getDropoffBranches()}`}
                                options={getDropoffBranches()}
                                defaultValue={getDropoffBranches()?.find(
                                  (val) =>
                                    // (changed &&
                                    //   (+val.value === +selectedDropoffBranch?.value ||
                                    //     +val.value === +selectedDropoffBranch)) ||
                                    !changed &&
                                    +val.value === +rentalDetails?.rentalDetails?.dropOffBranchId,
                                )}
                                placeholder={formatMessage({
                                  id: "select.Dropoff branch",
                                })}
                                onChange={(selection) => {
                                  setChanged(true);
                                  setCopounId(null);
                                  setCouponCode(null);
                                  // setTimeout(() => {
                                  setSelectedDropoffBranch(selection);
                                  if (selection?.value == selectedBranch?.value) {
                                    setIsPickSameReturn(true);
                                  }
                                  // }, 100);
                                }}
                                className="dropdown-select"
                                styles={customStyles}
                              />
                            </>
                          </div>
                        ) : null}

                        {(!bookingId &&
                          selectedBranch &&
                          carPlans?.length &&
                          selectedCar &&
                          bookingType == "rent-to-own") ||
                        (!bookingId &&
                          selectedBranch &&
                          carPlans?.length &&
                          selectedCar &&
                          bookingType == "rent-to-own") ||
                        (bookingId &&
                          selectedBranch &&
                          selectedCar &&
                          carPlans?.length &&
                          bookingType == "rent-to-own") ? (
                          <>
                            <h4 style={{ fontWeight: "bold" }} className="mt-4 mb-4">
                              <FormattedMessage id="rental.plans" />
                            </h4>
                            <div className="row mb-4">
                              {carPlans?.map((i) => (
                                <div
                                  key={i.id}
                                  className="d-flex col-4"
                                  onClick={() => setChanged(true)}
                                >
                                  <div className="">
                                    <input
                                      id={`plan-${i.id}`}
                                      name="plan"
                                      style={{ cursor: "pointer" }}
                                      type="radio"
                                      checked={i?.id == plan?.id}
                                      onChange={(e) => {
                                        setPlan({ id: i?.id, noOfMonths: i?.noOfMonths });
                                        setDropOffDate(
                                          moment(pickUpDate)
                                            .locale("en")
                                            .add(i?.noOfMonths, "months"),
                                        );
                                      }}
                                    />
                                  </div>
                                  <label
                                    htmlFor={`ally-extra-${i.id}`}
                                    style={{ cursor: "pointer" }}
                                  >
                                    <h5 style={{ fontSize: "16px" }}>
                                      <p className="m-0">
                                        {i.noOfMonths} <FormattedMessage id="month" />
                                      </p>
                                      <sub>
                                        <FormattedMessage id="1st Installment-booking" />
                                        {
                                          <FormattedMessage
                                            id="price.sr"
                                            values={{ price: i.firstInstallment }}
                                          />
                                        }{" "}
                                        <br />
                                        -
                                        <FormattedMessage id="final Installment" />
                                        <FormattedMessage
                                          id="price.sr"
                                          values={{ price: i.finalInstallment }}
                                        />
                                      </sub>
                                    </h5>
                                  </label>
                                </div>
                              ))}
                            </div>
                          </>
                        ) : null}
                        {(!bookingId &&
                          selectedBranch &&
                          (extraServices?.allyExtraServicesForAlly?.length ||
                            selectedBranch?.canDelivery) &&
                          selectedCar) ||
                        (!bookingId &&
                          selectedBranch &&
                          (extraServices?.branchExtraServices?.length ||
                            selectedBranch?.canDelivery) &&
                          selectedCar) ||
                        (bookingId && selectedBranch && selectedCar) ? (
                          <>
                            <h4 style={{ fontWeight: "bold" }} className="mt-4 mb-4">
                              <FormattedMessage id="rental.extraServices" />
                            </h4>
                            <div className="row mb-4" style={{ gap: "5px" }}>
                              {extraServices?.allyExtraServicesForAlly?.map((i) => (
                                <div
                                  key={i.id}
                                  className="d-flex "
                                  style={{ gap: "5px" }}
                                  onClick={() => setChanged(true)}
                                >
                                  <div className="">
                                    <input
                                      id={`ally-extra-${i.id}`}
                                      style={{ cursor: "pointer" }}
                                      type="checkbox"
                                      defaultChecked={
                                        i.isRequired ||
                                        rentalDetails?.rentalDetails?.rentalExtraServices
                                          ?.filter((s) => s.extraServiceType === "ally_company")
                                          ?.find((item) => +item.extraServiceId === +i.id) ||
                                        allyExtraServicesIds?.includes(i.id)
                                      }
                                      disabled={i.isRequired}
                                      onChange={(e) => {
                                        setChanged(true);
                                        e.target.checked
                                          ? setAllyExtraServicesIds([...allyExtraServicesIds, i.id])
                                          : setAllyExtraServicesIds(
                                              allyExtraServicesIds.filter(
                                                (item) => +item !== +i.id,
                                              ),
                                            );
                                      }}
                                    />
                                  </div>
                                  <label
                                    htmlFor={`ally-extra-${i.id}`}
                                    style={{ cursor: "pointer", margin: 0 }}
                                  >
                                    <h5 style={{ fontSize: "14px" }}>
                                      {i.extraService[`${locale}Title`]}
                                    </h5>
                                    <h6 style={{ fontSize: "12px" ,direction:"ltr"}}>
                                      {
                                        i.subtitle?.includes("SAR")  ||  i.subtitle?.includes("ريال")  ? 
                                    <RiyalComponent />
                                        
                                    
                                    : 
                                    ""
                                      }
                                      
                                       { i.subtitle?.includes("SAR") ? i.subtitle?.replace("SAR","") : i.subtitle?.replace("ريال","") }

                                      </h6>
                                  </label>
                                </div>
                              ))}
                              {(!bookingId && selectedCar.isUnlimited) ||
                              (bookingId &&
                                rentalDetails?.rentalDetails &&
                                selectedCar.isUnlimited) ? (
                                <div className="d-flex">
                                  <input
                                    type="checkbox"
                                    checked={unLimited}
                                    style={{ height: "19px" }}
                                    id="Unlimited.KM"
                                    onChange={(e) => {
                                      setChanged(true);
                                      setunLimited(e.target.checked);
                                    }}
                                  />
                                  <label
                                    htmlFor="Unlimited.KM"
                                    style={{ cursor: "pointer", margin: 0 }}
                                  >
                                    <h5 style={{ fontSize: "14px", margin: 0 }}>
                                      <FormattedMessage id="Unlimited.KM" />
                                    </h5>
                                    <h6 style={{ fontSize: "12px" ,direction:"rtl"}}>
                                      
                                        
                                     {selectedCar.unlimitedFeePerDay}
                                    <RiyalComponent />

                                    </h6>
                                  </label>
                                </div>
                              ) : null}

                              {extraServices?.branchExtraServices?.map((i) => (
                                <div
                                  key={i.id}
                                  className="d-flex"
                                  style={{ gap: "5px" }}
                                  onClick={() => setChanged(true)}
                                >
                                  <input
                                    id={`branch-extra-${i.id}`}
                                    style={{ cursor: "pointer", height: "19px" }}
                                    type="checkbox"
                                    defaultChecked={
                                      i.isRequired ||
                                      rentalDetails?.rentalDetails?.rentalExtraServices
                                        ?.filter((s) => s.extraServiceType === "branch")
                                        ?.find((item) => +item.extraServiceId === +i.id) ||
                                      branchExtraServicesIds?.includes(i.id)
                                    }
                                    disabled={i.isRequired}
                                    onChange={(e) => {
                                      setChanged(true);
                                      e.target.checked
                                        ? setBranchExtraServicesIds([
                                            ...branchExtraServicesIds,
                                            i.id,
                                          ])
                                        : setBranchExtraServicesIds(
                                            branchExtraServicesIds.filter(
                                              (item) => +item !== +i.id,
                                            ),
                                          );
                                    }}
                                  />

                                  <label
                                    htmlFor={`branch-extra-${i.id}`}
                                    style={{ cursor: "pointer", margin: 0 }}
                                  >
                                    <h5 style={{ fontSize: "14px" }}>
                                      {i.allyExtraService?.extraService[`${locale}Title`]}
                                    </h5>
                                    <h6 style={{ fontSize: "12px" ,direction:"ltr"}}>
                                      {
                                        i.subtitle?.includes("SAR")  ||  i.subtitle?.includes("ريال")  ? 
                                    <RiyalComponent />
                                        
                                    
                                    : 
                                    ""
                                      }
                                      
                                       { i.subtitle?.includes("SAR") ? i.subtitle?.replace("SAR","") : i.subtitle?.replace("ريال","") }

                                      </h6>
                                  </label>
                                </div>
                              ))}
                              {Boolean(selectedBranch?.canDelivery) && (
                                <div className="d-flex" style={{ gap: "5px" }}>
                                  <input
                                    id="delivery"
                                    style={{ cursor: "pointer", height: "19px" }}
                                    type="checkbox"
                                    checked={isDelivery}
                                    onChange={(e) => {
                                      setChanged(true);
                                      setIsDelivery(e.target.checked);
                                      if (!e.target.checked) {
                                        setSelectedDropoffBranch(null);
                                      }
                                    }}
                                  />

                                  <label
                                    htmlFor="delivery"
                                    style={{ cursor: "pointer", margin: 0 }}
                                  >
                                    <h5 style={{ fontSize: "14px" }}>
                                      <FormattedMessage id="delivery" />
                                    </h5>
                                  </label>
                                </div>
                              )}
                            </div>
                          </>
                        ) : null}
                        {selectedCompany && selectedBranch && selectedCar && isDelivery && (
                          <div className="mt-2">
                            <CustomTextField
                              fullWidth
                              name="dsitance"
                              value={distanceCarUser}
                              disabled
                            />
                            <CustomTextField
                              key={deliveryPrice}
                              fullWidth
                              // disabled
                              name="delivery_price"
                              onInput={(e) => {
                                if (e.target.value?.includes(".")) {
                                  e.target.value = e.target.value.toString().slice(0, 9);
                                } else {
                                  e.target.value = e.target.value.toString().slice(0, 6);
                                }
                              }}
                              defaultValue={deliveryPrice}
                              onBlur={(e) => {
                                setDeliveryPrice(+e.target.value);
                                setDeliveryChanged(true);
                                
                              }}
                            />
                          </div>
                        )}
                        {!isHandover &&
                        isDelivery &&
                        selectedCompany &&
                        selectedBranch &&
                        selectedCar &&
                        selectedBranch?.canHandover &&
                        distanceCarUser ? (
                          <>
                            <h4 className="mt-4 mb-4">
                              <Checkbox
                                checked={isTwoWays}
                                color="primary"
                                onChange={(e) => {
                                  setChanged(true);
                                  setIsTwoWays(!isTwoWays);
                                  // if (e.target.checked) {
                                  //   setIsPickSameReturn(true);
                                  // }
                                }}
                                inputProps={{ "aria-label": "secondary checkbox" }}
                              />
                              <FormattedMessage id="Return to the same delivery location" />
                            </h4>

                            <>
                            {
                              selectedBranch && 
                              <CustomTextField
                              key={handoverprice}
                              fullWidth
                              disabled
                              value={handoverprice}
                              name="handover_price"
                              defaultValue={handoverprice}
                            
                            />
                            }
                            
                            </>
                          </>
                        ) : null}
                        {(isHandover || isTwoWays) &&
                        selectedCompany &&
                        selectedBranch &&
                        selectedCar &&
                        bookingType != "rent-to-own" ? (
                          <>
                            <h6 className="mt-1 mb-3">
                              {formatMessage({
                                id: "handover_branch_price",
                              })}
                            </h6>
                            <CustomTextField
                              key={BookingPriceRes?.aboutRentPrice?.handoverprice}
                              defaultValue={rentalDetails?.rentalDetails?.handoverPrice ||  BookingPriceRes?.aboutRentPrice?.handoverprice}
                              fullWidth
                              placeholder={formatMessage({
                                id: "handover_branch_price",
                              })}
                              noLabel
                              name="handover_branch_price"
                              value={handoverprice2}
                              // disabled
                              onInput={(e) => {
                                if (e.target.value?.includes(".")) {
                                  e.target.value = e.target.value.toString().slice(0, 9);
                                } else {
                                  e.target.value = e.target.value.toString().slice(0, 6);
                                }
                              }}
                              onChange={(e) => {
                                setHandOverPrice2(+e.target.value);
                                setHandoverChanged(true);
                              }}
                            />
                          </>
                        ) : null}
                        {selectedCompany && selectedBranch && selectedCar ? (
                          <h4 style={{ fontWeight: "bold" }} className="mt-4 mb-4">
                            <FormattedMessage id="Discount coupon" />
                          </h4>
                        ) : null}
                        {selectedCompany && selectedBranch && selectedCar ? (
                          <div className="row">
                            <div className="col-md-4">
                              <CustomTextField
                                id="CouponCode"
                                fullWidth
                                name="Discount_coupon"
                                defaultValue={
                                  copounCode
                                    ? copounCode
                                    : copounref.current
                                    ? copounref.current
                                    : null
                                }
                                value={copounCode ? copounCode : ""}
                                onChange={(e) => {
                                  copounref.current = e.target.value;
                                  setCouponCode(e.target.value);
                                  // setChanged(true)
                                }}
                                error={couponAvailabilty}
                                errormsg={couponAvailabilty}
                              />
                            </div>
                            <div
                              className="col-md-4 align-items-center"
                              style={{ justifyItems: "center" }}
                            >
                              <div className="row " style={{ gap: "8px" }} disabled={!copounCode}>
                                <button
                                  onClick={() => {
                                    if (copounCode) {
                                      getCarCouponAvailable({
                                        variables: {
                                          carId: selectedCar?.id,
                                          couponCode: copounref.current,
                                          userId:
                                            customerDetails?.users?.collection[0]?.id ||
                                            rentalDetails?.rentalDetails?.userId,
                                          rentalId:bookingId
                                        },
                                      }).then((res) => {
                                        if (!res?.data?.carCouponAvailability?.status) {
                                          NotificationManager.error(
                                            <FormattedMessage id="Invalid coupon" />,
                                          );
                                          setCouponAvailability("Invalid coupon");
                                        } else {
                                          setChanged(true);
                                          setCopounId(res?.data?.carCouponAvailability?.coupon?.id);
                                          setCouponAvailability(null);
                                        }
                                      });
                                    } else {
                                      return;
                                    }
                                  }}
                                  className="btn btn-primary"
                                >
                                  <FormattedMessage id="apply" />
                                </button>
                                <button
                                  className="btn btn-danger"
                                  disabled={!copounCode}
                                  onClick={() => {
                                    setCopounId(undefined);
                                    setCouponCode(undefined);
                                    copounref.current = null;
                                    setCouponAvailability(null);
                                    setChanged(true);
                                  }}
                                >
                                  <FormattedMessage id="delete" />
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : null}

                        {selectedCar?.carInsurances?.length
                          ? (!bookingId ||
                              (bookingId && insuranceId) ||
                              (selectedCar?.carInsurances &&
                                rentalDetails?.rentalDetails.subStatus == "ally_declined")) && (
                              <Select
                                id="insurance"
                                className="mt-4"
                                options={selectedCar?.carInsurances?.map((i) => ({
                                  label: i.insuranceName,
                                  value: i.id,
                                }))}
                                disabled={
                                  !selectedCar || !Array.isArray(selectedCar?.carInsurances)
                                }
                                value={selectedCar?.carInsurances
                                  ?.map((i) => ({ label: i.insuranceName, value: i.id }))
                                  ?.find((i) => i.value == insuranceId)}
                                onChange={(selection) => {
                                  setInsuranceId(selection.value);
                                  setInsuranceChanged(true)
                                  setInsuranceName(selection.label);
                                }}
                                error={couponAvailabilty}
                                errormsg={couponAvailabilty}
                              />
                            )
                          : null}

                        {selectedCompany &&
                        selectedBranch &&
                        selectedCar &&
                        ((bookingType == "rent-to-own" && plan?.id) ||
                          bookingType != "rent-to-own") ? (
                          <div className="alert alert-info mt-2 mb-2" role="alert">
                            <p>{`${selectedCar?.make?.[`${locale}Name`]} - ${
                              selectedCar?.carModel?.[`${locale}Name`]
                            } - ${
                              selectedCar?.carVersion?.[`${locale}Name`]
                                ? selectedCar?.carVersion?.[`${locale}Name`]
                                : "s"
                            } - ${selectedCar.year}`}</p>

                            <BookingPriceSummary
                              isHandover={isHandover || isTwoWays}
                              BookingPrice={changed || InsuranceChanged || deliveryChanged || handoverChanged ? BookingPriceRes : {}}
                              calculatingPrice={calculatingPrice}
                              insurance={insuranceId}
                              BookingDetails={rentalDetails}
                              isUnlimited={unLimited}
                              bookingType={bookingType}
                              change={changed || InsuranceChanged || deliveryChanged || handoverChanged}
                              plan={plan}
                              _handoverPrice={handoverprice2}
                              deliveryType={
                                isDelivery && isTwoWays
                                  ? "two_ways"
                                  : isDelivery
                                  ? "one_way"
                                  : "no_delivery"
                              }
                              insuranceName={insuranceName}
                            />
                          </div>
                        ) : null}
                      </div>

                      {selectedCar &&
                        ((insuranceId && bookingType != "rent-to-own") ||
                          (!insuranceId && bookingType == "rent-to-own") ||
                          (insuranceId && bookingType == "rent-to-own")) && (
                          <>
                            <FormControlLabelContainer labelId="paymetMethod">
                              <RadioGroupContainer value={paymentMethod}>
                                {["cash", "online"].map((type) => (
                                  <FormControlLabel
                                    key={type}
                                    disabled={!selectedCar?.id || (type == "online" && bookingId &&  rentalDetails?.rentalDetails?.isPaid) || rentalDetails?.rentalDetails?.isPaid ||  rentalDetails?.rentalDetails?.pendingPaymentOrder}
                                    value={type.toUpperCase()}
                                    control={<Radio color="primary" />}
                                    checked={paymentMethod?.toLocaleLowerCase() == type}
                                    label={formatMessage({ id: type.toUpperCase() })}
                                    className="m-0"
                                    onChange={(e) => {
                                      setChanged(true);
                                      if (e.target.value == "ONLINE") {
                                        setPaymentMethod("ONLINE");
                                        return;
                                      }
                                      setPaymentMethod("CASH");
                                    }}
                                  />
                                ))}
                              </RadioGroupContainer>
                            </FormControlLabelContainer>
                            {paymentMethod != "CASH" ? (
                              <div>
                                <h4 style={{ fontWeight: "bold" }} className="mt-4 mb-4">
                                  <Checkbox
                                    disabled={bookingId}
                                    checked={FursanChecked}
                                    color="primary"
                                    onChange={() => {
                                      setFusranChecked(!FursanChecked);
                                      setIsOpen(!FursanChecked);
                                    }}
                                    inputProps={{ "aria-label": "secondary checkbox" }}
                                  />
                                  <FormattedMessage id="Alfursan" />
                                </h4>
                              </div>
                            ) : null}
                            <CustomTextField
                              id="suggestedPricePerDay"
                              fullWidth
                              name="suggestedPricePerDay"
                              defaultValue={suggestedPricePerDayRef.current}
                              onChange={(e) => {
                                const price = e.target.value;
                                if (/^[0-9]+(\.)?[0-9]*$/.test(price.toString()) || price === "") {
                                  suggestedPricePerDayRef.current = e.target.value;
                                }
                              }}
                            />
                            {bookingId && (
                              <TextField
                                id="note"
                                label={<FormattedMessage id="note" />}
                                multiline
                                style={{ marginBottom: "-50px" }}
                                rows={4}
                                defaultValue={Noteref.current}
                                variant="outlined"
                                // onFocus={(e) => {
                                //   setChanged(true);
                                //   window.setTimeout(() => {
                                //     document.querySelector("#note").focus();
                                //   }, 1000);
                                // }}

                                onChange={(e) => {
                                  Noteref.current = e.target.value;
                                }}
                              />
                            )}
                          </>
                        )}
                    </>
                  ) : null}
                  <div style={{ marginTop: "80px" }}>
                    <button
                      type="submit"
                      style={{ width: "100%" }}
                      className="btn btn-primary text-white btn-icon mt-2 mb-2"
                      onClick={(e) => handleSubmitRent(e)}
                      disabled={
                        !(selectedCompany && selectedBranch && selectedCar) ||
                        (!insuranceId && bookingType != "rent-to-own")
                      }
                    >
                      <FormattedMessage id={bookingId ? "button.save" : "Rent"} />
                    </button>
                  </div>
                </div>
                <div className="col-md-5" style={{ height: "fit-content" }}>
                  <CustomerDataDisplay
                    customerDetailsRes={customerDetails || customerDetailsRes}
                    walletBalance={walletBalance}
                  />
                </div>
              </div>
            )}
          </TabPanel>
          <TabPanel style={{ height: "100%" }}>
            <div className="col-md-12" style={{ height: "100%", overflowX: "auto" }}>
              <InstallmentsTable
                rentalId={rentalDetails?.rentalDetails?.id}
                Installments={rentalDetails?.rentalDetails?.mergedInstallments}
                refetchBooking={refetchBooking}
                bookDetails={rentalDetails?.rentalDetails}
              />
            </div>
          </TabPanel>
        </div>
      </Tabs>

      <Modals
        {...{
          extensionModalOpen,
          setIsExtensionModalOpen,
          rentalDetails,
          refetchBooking,
          opneTimeLineModal,
          setOpenTimeLineModal,
          bookingId,
          OpenRejectionModal,
          setOpenRejectionModal,
        }}
        rentalDetails={rentalDetails}
        company={allCompanies?.find((val) => val.value == selectedCompany?.value)}
      />
      <FursanVerification
        isOpen={isopen}
        setIsOpen={setIsOpen}
        setFusranChecked={setFusranChecked}
        customerDetailsRes={customerDetails || customerDetailsRes}
        setFursanVerified={setFursanVerified}
        FursanVerified={FursanVerified}
      />
    </>
  );

  function carName(c) {
    return `${c.transmissionName} / ${c.make?.[`${locale}Name`]} - ${
      c?.carModel?.[`${locale}Name`]
    } - ${c?.carVersion?.[`${locale}Name`] ? c?.carVersion?.[`${locale}Name`] : ""} - ${
      c.year
    } | [${formatMessage({ id: "branchName" })}: ${c.branch[`${locale}Name`]}]
    [${formatMessage({ id: "daily" })}: ${c?.dailyPrice} ${formatMessage({
      id: "rental.weeklyPrice",
    })}: ${c?.weeklyPrice ? c?.weeklyPrice : ""} ${formatMessage({
      id: "rental.monthlyPrice",
    })}: ${c?.monthlyPrice}]`;
  }
}

export default AddEditBooking;
